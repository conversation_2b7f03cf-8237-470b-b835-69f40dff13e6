const { db } = require('./db/database');
const Subscription = require('./models/Subscription');

console.log('=== Detailed Streaming Limit Issue Analysis ===\n');

async function analyzeStreamingIssues() {
  try {
    // 1. Check users with streams but getting limit errors
    console.log('1. Analyzing users with existing streams:');
    
    const usersWithStreams = await new Promise((resolve, reject) => {
      db.all(`SELECT u.id, u.username, u.plan_type, u.max_streaming_slots, COUNT(s.id) as stream_count
              FROM users u 
              LEFT JOIN streams s ON u.id = s.user_id 
              WHERE u.plan_type IN ('PodLite', 'PodFlow', 'PodPrime') 
              GROUP BY u.id, u.username, u.plan_type, u.max_streaming_slots
              HAVING stream_count > 0
              ORDER BY stream_count DESC
              LIMIT 10`, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    for (const user of usersWithStreams) {
      console.log(`\n   User: ${user.username} (${user.plan_type})`);
      console.log(`   - User slots: ${user.max_streaming_slots}`);
      console.log(`   - Stream count: ${user.stream_count}`);
      
      // Check subscription details
      const subscription = await Subscription.getUserSubscription(user.id);
      if (subscription) {
        console.log(`   - Active subscription: ${subscription.plan_name} (${subscription.max_streaming_slots} slots)`);
        console.log(`   - Subscription status: ${subscription.status}`);
        console.log(`   - End date: ${subscription.end_date}`);
      } else {
        console.log(`   - No active subscription found`);
      }
      
      // Test quota check
      const quotaCheck = await Subscription.checkStreamingSlotLimit(user.id);
      console.log(`   - Quota check: hasLimit=${quotaCheck.hasLimit}, current=${quotaCheck.currentSlots}, max=${quotaCheck.maxSlots}`);
      
      // Check stream statuses
      const streamStatuses = await new Promise((resolve, reject) => {
        db.all('SELECT status, COUNT(*) as count FROM streams WHERE user_id = ? GROUP BY status', [user.id], (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      });
      
      console.log(`   - Stream statuses:`, streamStatuses.map(s => `${s.status}:${s.count}`).join(', '));
    }
    
    // 2. Test the middleware logic
    console.log('\n\n2. Testing Middleware Logic:');
    
    // Simulate the checkStreamingSlotLimit function behavior
    const testUserId = usersWithStreams.length > 0 ? usersWithStreams[0].id : null;
    
    if (testUserId) {
      console.log(`\n   Testing with user: ${usersWithStreams[0].username}`);
      
      // Step 1: Get subscription
      const subscription = await Subscription.getUserSubscription(testUserId);
      console.log(`   - Subscription found: ${!!subscription}`);
      
      let maxSlots = 0;
      if (subscription) {
        maxSlots = subscription.max_streaming_slots;
        console.log(`   - Max slots from subscription: ${maxSlots}`);
      } else {
        // Get from user table
        const user = await new Promise((resolve, reject) => {
          db.get('SELECT max_streaming_slots FROM users WHERE id = ?', [testUserId], (err, row) => {
            if (err) reject(err);
            else resolve(row);
          });
        });
        maxSlots = user ? user.max_streaming_slots : 0;
        console.log(`   - Max slots from user table: ${maxSlots}`);
      }
      
      // Step 2: Count streams
      const currentStreams = await new Promise((resolve, reject) => {
        db.get("SELECT COUNT(*) as count FROM streams WHERE user_id = ?", [testUserId], (err, row) => {
          if (err) reject(err);
          else resolve(row.count);
        });
      });
      
      console.log(`   - Current stream count: ${currentStreams}`);
      
      // Step 3: Check limit
      const hasLimit = maxSlots !== -1 && currentStreams >= maxSlots;
      console.log(`   - Has limit: ${hasLimit} (${currentStreams} >= ${maxSlots})`);
      
      if (hasLimit) {
        console.log(`   - ERROR: This user would get "streaming limit reached" error!`);
        console.log(`   - Issue: User has ${currentStreams} streams but only ${maxSlots} slots allowed`);
      } else {
        console.log(`   - OK: User can create more streams`);
      }
    }
    
    // 3. Check for discrepancies
    console.log('\n\n3. Checking for Plan/Subscription Discrepancies:');
    
    const discrepancies = await new Promise((resolve, reject) => {
      db.all(`SELECT u.id, u.username, u.plan_type, u.max_streaming_slots as user_slots, 
                     sp.name as sub_plan, sp.max_streaming_slots as sub_slots
              FROM users u 
              JOIN user_subscriptions us ON u.id = us.user_id 
              JOIN subscription_plans sp ON us.plan_id = sp.id 
              WHERE us.status = 'active' 
              AND (u.plan_type != sp.name OR u.max_streaming_slots != sp.max_streaming_slots)
              LIMIT 10`, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    if (discrepancies.length > 0) {
      console.log('   Found discrepancies:');
      discrepancies.forEach(disc => {
        console.log(`   - ${disc.username}: user_plan=${disc.plan_type}(${disc.user_slots} slots) vs subscription=${disc.sub_plan}(${disc.sub_slots} slots)`);
      });
    } else {
      console.log('   No discrepancies found between user plans and subscriptions');
    }
    
  } catch (error) {
    console.error('Error during analysis:', error);
  }
  
  console.log('\n=== Analysis Complete ===');
  process.exit(0);
}

analyzeStreamingIssues();
