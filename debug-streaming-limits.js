const { db } = require('./db/database');

console.log('=== StreamOnPod Streaming Limit Investigation ===\n');

// Check subscription plans
console.log('1. Subscription Plans Configuration:');
db.all('SELECT name, max_streaming_slots, max_storage_gb, price FROM subscription_plans WHERE is_active = 1 ORDER BY price ASC', [], (err, plans) => {
  if (err) {
    console.error('Error getting plans:', err);
    return;
  }
  
  plans.forEach(plan => {
    console.log(`   - ${plan.name}: ${plan.max_streaming_slots} slots, ${plan.max_storage_gb}GB storage, ${plan.price} price`);
  });
  
  console.log('\n2. Users with PodLite plan:');
  db.all('SELECT id, username, plan_type, max_streaming_slots FROM users WHERE plan_type = ? LIMIT 5', ['PodLite'], (err, users) => {
    if (err) {
      console.error('Error getting users:', err);
      return;
    }
    
    if (users.length === 0) {
      console.log('   No users found with PodLite plan');
      checkActiveSubscriptions();
      return;
    }
    
    users.forEach(user => {
      console.log(`   - ${user.username}: plan_type=${user.plan_type}, max_streaming_slots=${user.max_streaming_slots}`);
    });
    
    // Check stream counts for first user
    const firstUser = users[0];
    console.log(`\n3. Stream analysis for user: ${firstUser.username}`);
    
    db.get('SELECT COUNT(*) as total FROM streams WHERE user_id = ?', [firstUser.id], (err, totalResult) => {
      if (err) {
        console.error('Error counting total streams:', err);
        return;
      }
      
      db.get('SELECT COUNT(*) as active FROM streams WHERE user_id = ? AND status IN (?, ?, ?)', 
        [firstUser.id, 'live', 'offline', 'scheduled'], (err, activeResult) => {
        if (err) {
          console.error('Error counting active streams:', err);
          return;
        }
        
        console.log(`   - Total streams: ${totalResult.total}`);
        console.log(`   - Active streams (live/offline/scheduled): ${activeResult.active}`);
        
        // Test the checkStreamingSlotLimit function
        const Subscription = require('./models/Subscription');
        Subscription.checkStreamingSlotLimit(firstUser.id).then(quotaInfo => {
          console.log(`   - Quota check result: hasLimit=${quotaInfo.hasLimit}, currentSlots=${quotaInfo.currentSlots}, maxSlots=${quotaInfo.maxSlots}`);
          
          checkActiveSubscriptions();
        }).catch(err => {
          console.error('Error checking quota:', err);
          checkActiveSubscriptions();
        });
      });
    });
  });
});

function checkActiveSubscriptions() {
  console.log('\n4. Active Subscriptions Analysis:');
  db.all(`SELECT us.user_id, u.username, u.plan_type, sp.name as subscription_plan, sp.max_streaming_slots as sub_slots, u.max_streaming_slots as user_slots
          FROM user_subscriptions us 
          JOIN users u ON us.user_id = u.id 
          JOIN subscription_plans sp ON us.plan_id = sp.id 
          WHERE us.status = 'active' AND sp.name IN ('PodLite', 'PodFlow', 'PodPrime') 
          LIMIT 10`, [], (err, subs) => {
    if (err) {
      console.error('Error getting active subscriptions:', err);
      return;
    }
    
    if (subs.length === 0) {
      console.log('   No active subscriptions found for paid plans');
    } else {
      subs.forEach(sub => {
        console.log(`   - ${sub.username}: plan_type=${sub.plan_type}, subscription=${sub.subscription_plan}, sub_slots=${sub.sub_slots}, user_slots=${sub.user_slots}`);
      });
    }
    
    console.log('\n=== Investigation Complete ===');
    process.exit(0);
  });
}
