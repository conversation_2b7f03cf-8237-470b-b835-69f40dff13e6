const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const ffmpegInstaller = require('@ffmpeg-installer/ffmpeg');
const schedulerService = require('./schedulerService');
const { v4: uuidv4 } = require('uuid');
const { db } = require('../db/database');
const cpuManager = require('../utils/cpuManager');
let ffmpegPath;
if (fs.existsSync('/usr/bin/ffmpeg')) {
  ffmpegPath = '/usr/bin/ffmpeg';
  console.log('Using system FFmpeg at:', ffmpegPath);
} else {
  ffmpegPath = ffmpegInstaller.path;
  console.log('Using bundled FFmpeg at:', ffmpegPath);
}
const Stream = require('../models/Stream');
const Video = require('../models/Video');
const notificationService = require('./notificationService');
const mkvOptimizer = require('./mkvOptimizer');
const {
  createStreamingError,
  createNotFoundError,
  createValidationError,
  logError,
  retryOperation
} = require('../utils/errorHandler');
const activeStreams = new Map();
const streamLogs = new Map();
const streamRetryCount = new Map();
const MAX_RETRY_ATTEMPTS = 3;
const manuallyStoppingStreams = new Set();
const failedStreams = new Set(); // Blacklist for streams that consistently fail
const streamFailureTimestamps = new Map(); // Track failure timestamps
const MAX_LOG_LINES = 100;

// Store for tracking stuck status detection
const stuckStatusTimers = new Map(); // Track stuck detection timers
const streamStartupPhases = new Map(); // Track current startup phase for each stream
const streamHealthTimers = new Map(); // Track health check timers for long-duration streams
const fileHandleHealthData = new Map(); // Track file handle health metrics for each stream
const youtubeAlertCooldowns = new Map(); // Track YouTube alert cooldowns to prevent spam

// Memory management constants
const CLEANUP_INTERVAL = 60 * 60 * 1000; // 1 hour
const DATA_RETENTION_TIME = 24 * 60 * 60 * 1000; // 24 hours
const MAX_FAILURE_HISTORY = 50; // Maximum failure records per stream

// Auto-stop configuration with startup grace period and graduated thresholds
const AUTO_STOP_CONFIG = {
  // Startup grace period - no auto-stop during this time
  STARTUP_GRACE_PERIOD_MS: 90000,     // 90 seconds - streams are protected during startup

  // Graduated thresholds based on stream age
  EARLY_STAGE_DURATION_MS: 120000,    // 2 minutes - early stage with lenient thresholds
  MATURE_STAGE_DURATION_MS: 600000,   // 10 minutes - mature stage with normal thresholds

  // Startup phase thresholds (0-2 minutes) - Very lenient
  STARTUP_MAX_CONSECUTIVE_FAILURES: 15,
  STARTUP_I_O_ERROR_THRESHOLD: 10,
  STARTUP_CONNECTION_ERROR_THRESHOLD: 10,

  // Early stage thresholds (2-10 minutes) - Moderate
  EARLY_MAX_CONSECUTIVE_FAILURES: 10,
  EARLY_I_O_ERROR_THRESHOLD: 7,
  EARLY_CONNECTION_ERROR_THRESHOLD: 7,

  // Mature stage thresholds (10+ minutes) - Normal
  MAX_CONSECUTIVE_FAILURES: 8,        // Max consecutive failures before auto-stop
  FAILURE_WINDOW_MINUTES: 10,         // Time window to count failures
  I_O_ERROR_THRESHOLD: 5,             // Max I/O errors before immediate stop
  CONNECTION_ERROR_THRESHOLD: 5       // Max connection errors before immediate stop
};

// Stuck status detection configuration - Enhanced with startup grace period
const STUCK_STATUS_CONFIG = {
  // Startup grace period - no stuck detection during this time
  STARTUP_GRACE_PERIOD: 45000,       // 45 seconds grace period for initial startup

  // Maximum time allowed in each startup phase before considering it stuck
  MAX_INITIALIZING_TIME: 60000,      // 60 seconds (increased from 30s)
  MAX_CONNECTING_TIME: 90000,         // 90 seconds (increased from 60s)
  MAX_BUFFERING_TIME: 120000,         // 120 seconds (increased from 90s)
  MAX_STABILIZING_TIME: 180000,       // 180 seconds (increased from 120s)
  MAX_TOTAL_STARTUP_TIME: 300000,     // 5 minutes total startup time (increased from 3m)

  // Check interval for stuck status detection
  STUCK_CHECK_INTERVAL: 20000,       // Check every 20 seconds (increased from 15s)

  // Timeout for streams that never progress
  ABSOLUTE_STARTUP_TIMEOUT: 420000   // 7 minutes absolute maximum (increased from 5m)
};

// Long-duration streaming health check configuration
const HEALTH_CHECK_CONFIG = {
  // Start health checks after stream is stable (30 minutes)
  HEALTH_CHECK_START_DELAY: 30 * 60 * 1000,  // 30 minutes

  // Check interval for long-duration streams
  HEALTH_CHECK_INTERVAL: 10 * 60 * 1000,     // 10 minutes

  // Maximum time without output before considering stream frozen
  MAX_SILENCE_DURATION: 5 * 60 * 1000,       // 5 minutes

  // Maximum consecutive health check failures before restart
  MAX_HEALTH_FAILURES: 3
};

// File handle health monitoring configuration
const FILE_HANDLE_HEALTH_CONFIG = {
  // Start monitoring after stream stabilizes
  MONITORING_START_DELAY: 45 * 60 * 1000,    // 45 minutes

  // Check interval for file handle health
  MONITORING_INTERVAL: 60 * 60 * 1000,       // Check every hour

  // Memory usage thresholds
  MEMORY_GROWTH_THRESHOLD: 300 * 1024 * 1024, // 300MB memory growth threshold
  ABSOLUTE_MEMORY_THRESHOLD: 800 * 1024 * 1024, // 800MB absolute memory threshold

  // Error frequency thresholds
  ERROR_FREQUENCY_THRESHOLD: 15,              // 15 errors per hour triggers concern
  CRITICAL_ERROR_THRESHOLD: 25,               // 25 errors per hour triggers action

  // Performance degradation indicators
  PERFORMANCE_CHECK_WINDOW: 30 * 60 * 1000,  // 30-minute performance window
  DEGRADATION_THRESHOLD: 0.75,                // 75% of baseline performance

  // YouTube-specific settings
  YOUTUBE_MANUAL_INTERVENTION_THRESHOLD: 20,   // Higher threshold for YouTube
  YOUTUBE_ALERT_COOLDOWN: 2 * 60 * 60 * 1000 // 2-hour cooldown between alerts
};

// Load balancing configuration
const LOAD_BALANCE_CONFIG = {
  CPU_CHECK_INTERVAL: 10000,          // Check CPU every 10 seconds
  QUALITY_CHANGE_COOLDOWN: 30000,     // Wait 30s between quality changes
  CPU_THRESHOLDS: {
    HIGH: 85,      // CPU > 85% = Minimal quality
    MEDIUM: 75,    // CPU 75-85% = Low quality
    LOW: 60        // CPU 60-75% = Medium quality
                   // CPU < 60% = Normal quality
  },
  QUALITY_PRESETS: {
    MINIMAL: { resolution: '360x240', bitrate: 1200, fps: 24, preset: 'ultrafast' },
    LOW: { resolution: '480x360', bitrate: 2000, fps: 30, preset: 'ultrafast' },
    MEDIUM: { resolution: '720x480', bitrate: 3500, fps: 30, preset: 'ultrafast' },
    NORMAL: { resolution: '1280x720', bitrate: 5000, fps: 30, preset: 'veryfast' }
  }
};

// Load balancing state
let currentCpuUsage = 0;
let currentQualityLevel = 'NORMAL';
let lastQualityChange = 0;
let loadBalancingEnabled = true;
let qualityChangeHistory = new Map(); // streamId -> last change time

// Load balancing functions
function determineQualityLevel(cpuUsage) {
  if (cpuUsage >= LOAD_BALANCE_CONFIG.CPU_THRESHOLDS.HIGH) {
    return 'MINIMAL';
  } else if (cpuUsage >= LOAD_BALANCE_CONFIG.CPU_THRESHOLDS.MEDIUM) {
    return 'LOW';
  } else if (cpuUsage >= LOAD_BALANCE_CONFIG.CPU_THRESHOLDS.LOW) {
    return 'MEDIUM';
  } else {
    return 'NORMAL';
  }
}

function getQualityPreset(qualityLevel) {
  return LOAD_BALANCE_CONFIG.QUALITY_PRESETS[qualityLevel] || LOAD_BALANCE_CONFIG.QUALITY_PRESETS.NORMAL;
}

function shouldChangeQuality(newQualityLevel) {
  const now = Date.now();
  const timeSinceLastChange = now - lastQualityChange;

  return (
    loadBalancingEnabled &&
    newQualityLevel !== currentQualityLevel &&
    timeSinceLastChange >= LOAD_BALANCE_CONFIG.QUALITY_CHANGE_COOLDOWN
  );
}

async function applyLoadBalancing(cpuUsage) {
  try {
    currentCpuUsage = cpuUsage;
    const newQualityLevel = determineQualityLevel(cpuUsage);

    if (shouldChangeQuality(newQualityLevel)) {
      console.log(`[LoadBalancer] CPU usage: ${cpuUsage}% - Changing quality from ${currentQualityLevel} to ${newQualityLevel}`);
      const activeStreamIds = Array.from(activeStreams.keys());
      if (activeStreamIds.length > 0) {
        await changeAllStreamsQuality(newQualityLevel);
        currentQualityLevel = newQualityLevel;
        lastQualityChange = Date.now();

        // Log quality change
        addLoadBalanceLog(`Quality changed to ${newQualityLevel} due to CPU usage: ${cpuUsage}%`);
      }
    }
  } catch (error) {
    console.error('[LoadBalancer] Error applying load balancing:', error);
  }
}

async function changeAllStreamsQuality(qualityLevel) {
  const activeStreamIds = Array.from(activeStreams.keys());
  const qualityPreset = getQualityPreset(qualityLevel);

  console.log(`[LoadBalancer] Applying ${qualityLevel} quality to ${activeStreamIds.length} active streams`);
  // Batch load all streams at once to prevent N+1 query problem
  const streams = await Stream.findByIds(activeStreamIds);
  const streamMap = new Map(streams.map(s => [s.id, s]));

  for (const streamId of activeStreamIds) {
    try {
      const now = Date.now();
      const lastChange = qualityChangeHistory.get(streamId) || 0;

      // Prevent too frequent changes per stream
      if (now - lastChange < LOAD_BALANCE_CONFIG.QUALITY_CHANGE_COOLDOWN) {
        continue;
      }

      const stream = streamMap.get(streamId);
      if (stream && stream.use_advanced_settings) {
        // Only apply load balancing to streams with advanced settings
        await restartStreamWithQuality(streamId, qualityPreset);
        qualityChangeHistory.set(streamId, now);

        addStreamLog(streamId, `[LoadBalancer] Quality changed to ${qualityLevel} (CPU: ${currentCpuUsage}%)`);
      }
    } catch (error) {
      console.error(`[LoadBalancer] Error changing quality for stream ${streamId}:`, error);
    }
  }
}

async function restartStreamWithQuality(streamId, qualityPreset) {
  try {
    // Get current stream info
    const stream = await Stream.findById(streamId);
    if (!stream) return;

    // Stop current stream
    await stopStream(streamId);

    // Wait a moment for cleanup
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Update stream with new quality settings
    await Stream.update(streamId, {
      resolution: qualityPreset.resolution,
      bitrate: qualityPreset.bitrate,
      fps: qualityPreset.fps
    });

    // Restart with new settings
    const result = await startStream(streamId);
    if (result.success) {
      console.log(`[LoadBalancer] Stream ${streamId} restarted with ${qualityPreset.resolution} quality`);
    } else {
      console.error(`[LoadBalancer] Failed to restart stream ${streamId}:`, result.error);
    }
  } catch (error) {
    console.error(`[LoadBalancer] Error restarting stream ${streamId}:`, error);
  }
}

// Load balance logging with production optimization
const loadBalanceLogs = [];
const isProduction = process.env.NODE_ENV === 'production';

function addLoadBalanceLog(message, level = 'info') {
  const timestamp = new Date().toISOString();
  const logEntry = `[${timestamp}] ${message}`;

  loadBalanceLogs.push(logEntry);
  if (loadBalanceLogs.length > MAX_LOG_LINES) {
    loadBalanceLogs.shift();
  }

  // In production, only log warnings and errors
  if (!isProduction || level === 'warn' || level === 'error') {
    if (level === 'error') {
      console.error(`[LoadBalancer] ${message}`);
    } else if (level === 'warn') {
      console.warn(`[LoadBalancer] ${message}`);
    } else {
      console.info(`[LoadBalancer] ${message}`);
    }
  }
}

function getLoadBalanceStatus() {
  return {
    enabled: loadBalancingEnabled,
    currentCpuUsage,
    currentQualityLevel,
    lastQualityChange: new Date(lastQualityChange).toISOString(),
    activeStreams: activeStreams.size,
    thresholds: LOAD_BALANCE_CONFIG.CPU_THRESHOLDS,
    qualityPresets: LOAD_BALANCE_CONFIG.QUALITY_PRESETS,
    recentLogs: loadBalanceLogs.slice(-10)
  };
}

// Function to clean up orphaned streams after server restart
async function restoreActiveStreams() {
  try {
    console.log('[StreamingService] Cleaning up orphaned streams...');
    const liveStreams = await Stream.findAll(null, 'live');

    if (liveStreams && liveStreams.length > 0) {
      console.log(`[StreamingService] Found ${liveStreams.length} streams marked as 'live' in database`);
      console.log('[StreamingService] Marking all as offline since server restarted (streams cannot survive restart)');

      for (const stream of liveStreams) {
        try {
          console.log(`[StreamingService] Marking stream ${stream.id} as offline (server restart cleanup)`);
          await Stream.updateStatus(stream.id, 'offline');
        } catch (error) {
          console.error(`[StreamingService] Error marking stream ${stream.id} as offline:`, error);
        }
      }
    } else {
      console.log('[StreamingService] No orphaned streams found to clean up');
    }
  } catch (error) {
    console.error('[StreamingService] Error during stream cleanup:', error);
  }
}

// Helper function to detect if video needs re-encoding
function needsReencoding(video, targetResolution, targetBitrate, targetFps) {
  if (!video.resolution || !video.bitrate || !video.fps) {
    return true; // Re-encode if we don't have metadata
  }

  // Check codec compatibility - HEVC videos and some MKV codecs need re-encoding for streaming
  if (video.codec) {
    const codecLower = video.codec.toLowerCase();
    if (codecLower.includes('hevc') || codecLower.includes('h265') ||
        codecLower.includes('vp9') || codecLower.includes('av1')) {
      console.log(`[StreamingService] Video uses ${video.codec} codec, re-encoding required for streaming compatibility`);
      return true;
    }
  }

  // Check container format - MKV files often need re-encoding for RTMP streaming
  if (video.format && video.format.toLowerCase() === 'mkv') {
    console.log(`[StreamingService] MKV container detected, re-encoding recommended for RTMP compatibility`);
    return true;
  }

  const [currentWidth, currentHeight] = video.resolution.split('x').map(Number);
  const [targetWidth, targetHeight] = targetResolution.split('x').map(Number);

  // Check if current video exceeds target parameters significantly
  const bitrateExceeds = video.bitrate > (targetBitrate * 1.5);
  const resolutionExceeds = (currentWidth > targetWidth * 1.2) || (currentHeight > targetHeight * 1.2);
  const fpsExceeds = video.fps > (targetFps * 1.2);

  return bitrateExceeds || resolutionExceeds || fpsExceeds;
}

// Helper function to get enhanced RTMP options for long-duration streaming
// Uses only FFmpeg options that are universally supported across builds
function getEnhancedRTMPOptions(rtmpUrl) {
  // Many RTMP-specific options are not supported in all FFmpeg builds
  // Use only the most basic and universally supported options
  return [
    // No RTMP-specific options - they're not universally supported
    // Basic network timeout (if supported)
    // '-timeout', '30000000'  // Even this might not be supported
  ];
}

// Helper function to get YouTube-specific RTMP input options
// These are input-level options that go before the input file
function getYouTubeInputOptions(rtmpUrl) {
  // Only add YouTube-specific options if this is actually a YouTube URL
  if (!rtmpUrl.includes('youtube.com') && !rtmpUrl.includes('rtmp.youtube.com')) {
    return [];
  }

  // Keep disabled until basic streaming works
  return [
    // RTMP-specific options not supported in this FFmpeg build
  ];
}

// Helper function to get YouTube-specific output options
// These are output-level options that go after the input file
function getYouTubeOutputOptions(rtmpUrl) {
  // Only add YouTube-specific options if this is actually a YouTube URL
  if (!rtmpUrl.includes('youtube.com') && !rtmpUrl.includes('rtmp.youtube.com')) {
    return [];
  }

  // Keep disabled until basic streaming works
  return [
    // Metadata options will be added back once basic streaming works
  ];
}

// Helper function to get copy-mode compatible settings for a video
function getCopyModeCompatibleSettings(video) {
  if (!video.resolution || !video.bitrate || !video.fps) {
    return null; // Can't determine compatibility without metadata
  }

  // Check codec compatibility
  if (video.codec) {
    const codecLower = video.codec.toLowerCase();
    if (codecLower.includes('hevc') || codecLower.includes('h265') ||
        codecLower.includes('vp9') || codecLower.includes('av1')) {
      return null; // Incompatible codec
    }
  }

  // Check container format
  if (video.format && video.format.toLowerCase() === 'mkv') {
    return null; // MKV needs re-encoding for RTMP
  }

  const [currentWidth, currentHeight] = video.resolution.split('x').map(Number);

  // Calculate maximum compatible settings (with some safety margin)
  const maxBitrate = Math.floor(video.bitrate * 1.4); // 40% margin
  const maxFps = Math.floor(video.fps * 1.1); // 10% margin

  // Available resolution options that are compatible
  const allResolutions = [
    { value: '480x360', width: 480, height: 360, label: '360p (480x360)' },
    { value: '640x480', width: 640, height: 480, label: '480p (640x480)' },
    { value: '854x480', width: 854, height: 480, label: '480p Wide (854x480)' },
    { value: '1280x720', width: 1280, height: 720, label: '720p HD (1280x720)' },
    { value: '1920x1080', width: 1920, height: 1080, label: '1080p FHD (1920x1080)' },
    { value: '2560x1440', width: 2560, height: 1440, label: '1440p QHD (2560x1440)' },
    { value: '3840x2160', width: 3840, height: 2160, label: '2160p 4K (3840x2160)' }
  ];

  // Determine video orientation
  const isVideoVertical = currentHeight > currentWidth;

  // Only return copy-mode compatible resolutions (no upscaling, no re-encoding)
  const compatibleResolutions = allResolutions.filter(res => {
    // For vertical videos, compare with vertical resolution options
    if (isVideoVertical) {
      // For vertical video (1080x1920), check against vertical resolution options
      const verticalWidth = res.height; // In vertical mode, height becomes width
      const verticalHeight = res.width; // In vertical mode, width becomes height
      return verticalWidth <= currentWidth * 1.1 && verticalHeight <= currentHeight * 1.1;
    } else {
      // For horizontal videos, use standard comparison
      return res.width <= currentWidth * 1.1 && res.height <= currentHeight * 1.1;
    }
  });

  // For backward compatibility, also provide copyModeResolutions
  const copyModeResolutions = compatibleResolutions;

  // Available bitrate options that are compatible
  const bitrateOptions = [1000, 1500, 2000, 2500, 3000, 4000, 5000, 6000, 8000, 10000]
    .filter(bitrate => bitrate <= maxBitrate);

  // Available FPS options that are compatible
  const fpsOptions = [15, 20, 24, 25, 30, 50, 60, 120]
    .filter(fps => fps <= maxFps);

  return {
    maxBitrate,
    maxFps,
    compatibleResolutions,
    copyModeResolutions,
    bitrateOptions,
    fpsOptions,
    videoProperties: {
      resolution: video.resolution,
      bitrate: video.bitrate,
      fps: video.fps,
      codec: video.codec,
      format: video.format
    }
  };
}

// Helper function to detect available hardware acceleration
function getHardwareAcceleration() {
  // For now, we'll use software encoding for maximum compatibility
  // In the future, this can be enhanced to detect NVENC, QSV, etc.
  return 'none';
}

// Helper function to calculate optimal bitrate for copy mode
function getOptimalCopyModeBitrate(video, userBitrate = null) {
  // For copy mode, we can use higher bitrates since no CPU encoding is involved
  // Prioritize user setting, then fall back to video-based optimization

  // If user specified a bitrate, use it (with reasonable caps for bandwidth)
  if (userBitrate && userBitrate > 0) {
    // Allow high bitrates for copy mode, cap at 15Mbps for bandwidth safety
    return Math.min(userBitrate, 15000);
  }

  if (!video.resolution) {
    return 4000; // Default high bitrate for copy mode
  }

  const [width, height] = video.resolution.split('x').map(Number);
  const totalPixels = width * height;

  // Calculate optimal bitrate based on resolution (fallback when no user setting)
  // Higher resolution = higher bitrate (since copy mode doesn't use CPU)
  if (totalPixels >= 1920 * 1080) {
    // 1080p and above - use high bitrate for best quality
    return Math.min(video.bitrate || 6000, 12000); // Increased cap for 1080p+
  } else if (totalPixels >= 1280 * 720) {
    // 720p - use medium-high bitrate
    return Math.min(video.bitrate || 4000, 8000); // Increased cap for 720p
  } else if (totalPixels >= 854 * 480) {
    // 480p - use medium bitrate
    return Math.min(video.bitrate || 2500, 5000); // Increased cap for 480p
  } else {
    // Lower resolutions - use moderate bitrate
    return Math.min(video.bitrate || 1500, 3000); // Increased cap for lower res
  }
}

function addStreamLog(streamId, message) {
  if (!streamLogs.has(streamId)) {
    streamLogs.set(streamId, []);
  }
  const logs = streamLogs.get(streamId);
  logs.push({
    timestamp: new Date().toISOString(),
    message
  });
  if (logs.length > MAX_LOG_LINES) {
    logs.shift();
  }
}

// Memory management functions
function cleanupStreamData(streamId) {
  console.log(`[MemoryCleanup] Cleaning up data for stream ${streamId}`);
  // Remove from all tracking maps and sets
  activeStreams.delete(streamId);
  streamLogs.delete(streamId);
  streamRetryCount.delete(streamId);
  qualityChangeHistory.delete(streamId);
  streamFailureTimestamps.delete(streamId);
  manuallyStoppingStreams.delete(streamId);
  failedStreams.delete(streamId);

  // Clean up stuck status detection
  cleanupStuckStatusDetection(streamId);

  // Clean up health check monitoring
  cleanupHealthCheckMonitoring(streamId);

  // Clean up file handle health monitoring
  cleanupFileHandleHealthMonitoring(streamId);

  // Clean up MKV optimizer tracking
  mkvOptimizer.onMkvStreamStopped(streamId);

  console.log(`[MemoryCleanup] Cleaned up data for stream ${streamId}`);
}

function performPeriodicCleanup() {
  const now = Date.now();
  let cleanedItems = 0;

  console.log('[MemoryCleanup] Starting periodic cleanup...');
  // Clean old quality change history
  for (const [streamId, timestamp] of qualityChangeHistory.entries()) {
    if (now - timestamp > DATA_RETENTION_TIME) {
      qualityChangeHistory.delete(streamId);
      cleanedItems++;
    }
  }

  // Clean old failure timestamps and limit history size
  for (const [streamId, failures] of streamFailureTimestamps.entries()) {
    // Remove old failures
    const recentFailures = failures.filter(f => now - f.timestamp < DATA_RETENTION_TIME);

    // Limit history size
    if (recentFailures.length > MAX_FAILURE_HISTORY) {
      recentFailures.splice(0, recentFailures.length - MAX_FAILURE_HISTORY);
    }

    if (recentFailures.length === 0) {
      streamFailureTimestamps.delete(streamId);
      cleanedItems++;
    } else if (recentFailures.length !== failures.length) {
      streamFailureTimestamps.set(streamId, recentFailures);
      cleanedItems++;
    }
  }

  // Clean old stream logs for inactive streams
  for (const [streamId, logs] of streamLogs.entries()) {
    if (!activeStreams.has(streamId)) {
      // Check if the last log entry is old
      const lastLog = logs[logs.length - 1];
      if (lastLog && now - new Date(lastLog.timestamp).getTime() > DATA_RETENTION_TIME) {
        streamLogs.delete(streamId);
        cleanedItems++;
      }
    }
  }

  // Clean orphaned retry counts
  for (const streamId of streamRetryCount.keys()) {
    if (!activeStreams.has(streamId)) {
      streamRetryCount.delete(streamId);
      cleanedItems++;
    }
  }

  console.log(`[MemoryCleanup] Periodic cleanup completed. Cleaned ${cleanedItems} items.`);
  console.log(`[MemoryCleanup] Current memory usage: activeStreams=${activeStreams.size}, streamLogs=${streamLogs.size}, qualityChangeHistory=${qualityChangeHistory.size}, failureTimestamps=${streamFailureTimestamps.size}`);
}

// Enhanced stream health check function
async function performStreamHealthCheck() {
  try {
    const Stream = require('../models/Stream');
    const liveStreams = await Stream.findAll(null, 'live');
    let inconsistenciesFixed = 0;

    for (const stream of liveStreams) {
      const isReallyActive = activeStreams.has(stream.id);

      if (!isReallyActive) {
        // Stream is marked as live in DB but not active in memory
        console.log(`[HealthCheck] Found inconsistent stream ${stream.id}: DB says 'live' but not in memory`);
        await Stream.updateStatus(stream.id, 'offline', stream.user_id);
        inconsistenciesFixed++;

        // Clean up any scheduled terminations
        if (typeof schedulerService !== 'undefined' && schedulerService.handleStreamStopped) {
          schedulerService.handleStreamStopped(stream.id);
        }
      }
    }

    // Check for streams in memory but not in DB as live
    for (const [streamId, process] of activeStreams.entries()) {
      try {
        const stream = await Stream.findById(streamId);
        if (!stream || stream.status !== 'live') {
          console.log(`[HealthCheck] Found orphaned process for stream ${streamId}: in memory but DB status is ${stream?.status || 'not found'}`);
          // Stop the orphaned process
          try {
            process.kill('SIGTERM');
            setTimeout(() => {
              if (activeStreams.has(streamId)) {
                process.kill('SIGKILL');
              }
            }, 5000);
          } catch (killError) {
            console.error(`[HealthCheck] Error killing orphaned process: ${killError.message}`);
          }

          activeStreams.delete(streamId);
          cleanupStreamData(streamId);
          inconsistenciesFixed++;
        }
      } catch (error) {
        console.error(`[HealthCheck] Error checking stream ${streamId}:`, error);
      }
    }

    if (inconsistenciesFixed > 0) {
      console.log(`[HealthCheck] Fixed ${inconsistenciesFixed} stream status inconsistencies`);
    }

  } catch (error) {
    console.error('[HealthCheck] Error during stream health check:', error);
  }
}

async function buildFFmpegArgs(stream) {
  const video = await Video.findById(stream.video_id);
  if (!video) {
    throw new Error(`Video record not found in database for video_id: ${stream.video_id}`);
  }
  // Use streaming-ready path if available, otherwise use original
  const preferredPath = video.streaming_ready_path || video.filepath;
  const relativeVideoPath = preferredPath.startsWith('/') ? preferredPath.substring(1) : preferredPath;
  const projectRoot = path.resolve(__dirname, '..');
  const videoPath = path.join(projectRoot, 'public', relativeVideoPath);

  // Normalize path for FFmpeg compatibility (especially on Windows)
  const normalizedVideoPath = path.normalize(videoPath).replace(/\\/g, '/');
  if (!fs.existsSync(videoPath)) {
    // If streaming-ready file doesn't exist but original does, use original
    if (video.streaming_ready_path && preferredPath === video.streaming_ready_path) {
      console.warn(`[StreamingService] Streaming-ready file not found for ${stream.id}, falling back to original file`);
      const originalRelativePath = video.filepath.startsWith('/') ? video.filepath.substring(1) : video.filepath;
      const originalVideoPath = path.join(projectRoot, 'public', originalRelativePath);

      if (fs.existsSync(originalVideoPath)) {
        console.log(`[StreamingService] Using original file for stream ${stream.id}: ${originalVideoPath}`);
        // Recursively call with original path
        const originalVideo = { ...video, streaming_ready_path: null };
        return buildFFmpegArgs({ ...stream }, originalVideo);
      }
    }

    console.error(`[StreamingService] CRITICAL: Video file not found on disk.`);
    console.error(`[StreamingService] Checked path: ${videoPath}`);
    console.error(`[StreamingService] stream.video_id: ${stream.video_id}`);
    console.error(`[StreamingService] video.filepath (from DB): ${video.filepath}`);
    console.error(`[StreamingService] video.streaming_ready_path (from DB): ${video.streaming_ready_path}`);
    console.error(`[StreamingService] Calculated relativeVideoPath: ${relativeVideoPath}`);
    console.error(`[StreamingService] process.cwd(): ${process.cwd()}`);
    throw new Error('Video file not found on disk. Please check paths and file existence.');
  }
  const rtmpUrl = `${stream.rtmp_url.replace(/\/$/, '')}/${stream.stream_key}`;
  const loopOption = '-stream_loop';
  const loopValue = stream.loop_video ? '-1' : '0';

  // If using streaming-ready file, we can always use copy mode for optimal performance
  if (video.streaming_ready_path && preferredPath === video.streaming_ready_path) {
    console.log(`[StreamingService] Using streaming-ready file for ${stream.id} - copy mode enabled for optimal performance`);
    // Optimized streaming-ready file configuration for long-duration stability
    const enhancedRTMPOptions = getEnhancedRTMPOptions(rtmpUrl);
    const youtubeInputOptions = getYouTubeInputOptions(rtmpUrl);
    const youtubeOutputOptions = getYouTubeOutputOptions(rtmpUrl);

    return [
      '-hwaccel', getHardwareAcceleration(),
      '-loglevel', 'error',
      '-re',
      '-fflags', '+genpts+discardcorrupt+flush_packets',
      '-avoid_negative_ts', 'make_zero',
      '-probesize', '32M',
      '-analyzeduration', '32M',
      ...enhancedRTMPOptions,
      ...youtubeInputOptions,
      loopOption, loopValue,
      '-i', normalizedVideoPath,
      '-c:v', 'copy',
      '-c:a', 'copy',
      ...youtubeOutputOptions,
      '-bsf:v', 'h264_mp4toannexb',
      '-f', 'flv',
      rtmpUrl
    ];
  }

  // Optimized basic copy mode - most efficient for streaming
  if (!stream.use_advanced_settings) {
    // Check if video codec is compatible with copy mode
    const isHEVC = video.codec && (video.codec.toLowerCase().includes('hevc') || video.codec.toLowerCase().includes('h265'));

    if (isHEVC) {
      // Force re-encoding for HEVC videos
      console.log(`[StreamingService] HEVC video detected, using re-encoding mode for stream ${stream.id}`);
      const resolution = '1280x720';
      const bitrate = 2500;
      const fps = 30;

      return [
        '-hwaccel', getHardwareAcceleration(),
        '-loglevel', 'error',
        '-re',
        '-fflags', '+genpts+discardcorrupt',
        '-avoid_negative_ts', 'make_zero',
        loopOption, loopValue,
        '-i', normalizedVideoPath,
        '-c:v', 'libx264',
        '-preset', 'ultrafast',
        '-tune', 'zerolatency',
        '-b:v', `${bitrate}k`,
        '-maxrate', `${bitrate * 1.2}k`,
        '-bufsize', `${bitrate * 1.5}k`,
        '-pix_fmt', 'yuv420p',
        '-g', '60',
        '-keyint_min', '60',
        '-sc_threshold', '0',
        '-s', resolution,
        '-r', fps.toString(),
        '-c:a', 'aac',
        '-b:a', '128k',
        '-ar', '44100',
        '-ac', '2',
        '-f', 'flv',
        '-flvflags', 'no_duration_filesize',
        rtmpUrl
      ];
    }

    // Check if video codec is compatible with copy mode
    const videoCodec = video.video_codec || 'unknown';

    if (videoCodec.toLowerCase().includes('h264') || videoCodec.toLowerCase().includes('avc')) {
      // Standard copy mode for H.264 videos with user-specified or optimal bitrate
      const userBitrate = stream.bitrate; // Get user setting from stream
      const optimalBitrate = getOptimalCopyModeBitrate(video, userBitrate);
      // console.log(`[StreamingService] Using copy mode for H.264 stream ${stream.id} with bitrate ${optimalBitrate}k (user: ${userBitrate}k)`); // Removed for production

      // Optimized copy mode for long-duration streaming stability
      // Remove conflicting bitrate controls that don't work with copy mode
      const enhancedRTMPOptions = getEnhancedRTMPOptions(rtmpUrl);
      const youtubeInputOptions = getYouTubeInputOptions(rtmpUrl);
      const youtubeOutputOptions = getYouTubeOutputOptions(rtmpUrl);

      return [
        '-hwaccel', getHardwareAcceleration(),
        '-loglevel', 'error',
        '-re',
        '-fflags', '+genpts+discardcorrupt+flush_packets',
        '-avoid_negative_ts', 'make_zero',
        '-probesize', '32M',
        '-analyzeduration', '32M',
        ...enhancedRTMPOptions,
        ...youtubeInputOptions,
        loopOption, loopValue,
        '-i', normalizedVideoPath,
        '-c:v', 'copy',
        '-c:a', 'copy',
        ...youtubeOutputOptions,
        '-bsf:v', 'h264_mp4toannexb',
        '-f', 'flv',
        rtmpUrl
      ];
    } else {
      // Force re-encoding for non-H.264 codecs to ensure compatibility
      console.log(`[StreamingService] Video codec ${videoCodec} not compatible with copy mode, using re-encoding for stream ${stream.id}`);
      const resolution = stream.resolution || '1280x720';
      const bitrate = stream.bitrate || 2500;
      const fps = stream.fps || 30;

      // Check if this is an MKV file and use optimizer
      const isMKV = video.format && video.format.toLowerCase() === 'mkv';

      if (isMKV) {
        try {
          // Use MKV optimizer for CPU-aware streaming
          const mkvResult = await mkvOptimizer.getOptimizedMkvParams(video, stream, []);
          addStreamLog(stream.id, mkvResult.recommendation);
          return mkvResult.params;
        } catch (mkvError) {
          // If MKV optimizer fails, throw the error to prevent streaming
          throw new Error(`MKV Streaming Error: ${mkvError.message}`);
        }
      }

      // Enhanced re-encoding for other complex formats
      const extraOptions = ['-fflags', '+genpts+discardcorrupt'];

      return [
        '-hwaccel', getHardwareAcceleration(),
        '-loglevel', 'error',
        '-re',
        ...extraOptions,
        '-avoid_negative_ts', 'make_zero',
        loopOption, loopValue,
        '-i', normalizedVideoPath,
        '-c:v', 'libx264',
        '-preset', 'ultrafast',
        '-tune', 'zerolatency',
        '-b:v', `${bitrate}k`,
        '-maxrate', `${bitrate * 1.2}k`,
        '-bufsize', `${bitrate * 1.5}k`,
        '-pix_fmt', 'yuv420p',
        '-g', '60',
        '-keyint_min', '60',
        '-sc_threshold', '0',
        '-s', resolution,
        '-r', fps.toString(),
        '-c:a', 'aac',
        '-b:a', '128k',
        '-ar', '44100',
        '-ac', '2',
        '-f', 'flv',
        '-flvflags', 'no_duration_filesize',
        rtmpUrl
      ];
    }
  }
  // Advanced settings with smart copy/encode decision
  const resolution = stream.resolution || '1280x720';
  const bitrate = stream.bitrate || 2500;
  const fps = stream.fps || 30;

  // Check if we can use copy mode even with advanced settings
  const shouldReencode = needsReencoding(video, resolution, bitrate, fps);

  if (!shouldReencode) {
    // Check if video codec is compatible with copy mode
    const isHEVC = video.codec && (video.codec.toLowerCase().includes('hevc') || video.codec.toLowerCase().includes('h265'));

    if (isHEVC) {
      console.log(`[StreamingService] HEVC video detected in advanced mode, forcing re-encoding for stream ${stream.id}`);
      // Force re-encoding for HEVC even in copy mode
    } else {
      // Use optimized copy mode for H.264 videos with user-specified bitrate
      const userBitrate = stream.bitrate; // Get user setting from advanced settings
      const optimalBitrate = getOptimalCopyModeBitrate(video, userBitrate);
      // console.log(`[StreamingService] Using optimized copy mode for stream ${stream.id} (video is compatible) with bitrate ${optimalBitrate}k (user: ${userBitrate}k)`); // Removed for production

      // Advanced copy mode optimized for long-duration streaming stability
      const enhancedRTMPOptions = getEnhancedRTMPOptions(rtmpUrl);
      const youtubeInputOptions = getYouTubeInputOptions(rtmpUrl);
      const youtubeOutputOptions = getYouTubeOutputOptions(rtmpUrl);

      return [
        '-hwaccel', getHardwareAcceleration(),
        '-loglevel', 'error',
        '-re',
        '-fflags', '+genpts+discardcorrupt+flush_packets',
        '-avoid_negative_ts', 'make_zero',
        '-probesize', '32M',
        '-analyzeduration', '32M',
        ...enhancedRTMPOptions,
        ...youtubeInputOptions,
        loopOption, loopValue,
        '-i', normalizedVideoPath,
        '-c:v', 'copy',
        '-c:a', 'copy',
        ...youtubeOutputOptions,
        '-bsf:v', 'h264_mp4toannexb',
        '-f', 'flv',
        rtmpUrl
      ];
    }
  }

  // Full re-encoding mode (only when necessary)
  // console.log(`[StreamingService] Using re-encoding mode for stream ${stream.id} (video needs optimization)`); // Removed for production

  // Check if this is an MKV file and use optimizer for advanced settings too
  const isMKV = video.format && video.format.toLowerCase() === 'mkv';

  if (isMKV) {
    try {
      // Use MKV optimizer for CPU-aware streaming in advanced mode
      const mkvResult = await mkvOptimizer.getOptimizedMkvParams(video, stream, []);
      addStreamLog(stream.id, `[Advanced] ${mkvResult.recommendation}`);
      return mkvResult.params;
    } catch (mkvError) {
      // If MKV optimizer fails, throw the error to prevent streaming
      throw new Error(`MKV Advanced Streaming Error: ${mkvError.message}`);
    }
  }

  // Enhanced re-encoding for other complex formats
  const extraOptions = ['-fflags', '+genpts+discardcorrupt'];

  return [
    '-hwaccel', getHardwareAcceleration(),
    '-loglevel', 'error',
    '-re',
    ...extraOptions,
    '-avoid_negative_ts', 'make_zero',
    loopOption, loopValue,
    '-i', normalizedVideoPath,
    '-c:v', 'libx264',
    '-preset', 'ultrafast', // Changed from 'veryfast' to 'ultrafast' for better performance
    '-tune', 'zerolatency', // Optimize for low latency streaming
    '-b:v', `${bitrate}k`,
    '-maxrate', `${bitrate * 1.2}k`, // Reduced from 1.5x to 1.2x
    '-bufsize', `${bitrate * 1.5}k`, // Reduced from 2x to 1.5x
    '-pix_fmt', 'yuv420p',
    '-g', '60',
    '-keyint_min', '60',
    '-sc_threshold', '0',
    '-s', resolution,
    '-r', fps.toString(),
    '-c:a', 'aac',
    '-b:a', '128k',
    '-ar', '44100',
    '-ac', '2',
    '-f', 'flv',
    '-flvflags', 'no_duration_filesize',
    rtmpUrl
  ];
}
async function startStream(streamId) {
  try {
    // Validate stream ID
    if (!streamId) {
      throw createValidationError('Stream ID is required', null, 'streamId');
    }

    // Check if stream is blacklisted due to repeated failures
    if (failedStreams.has(streamId)) {
      throw createStreamingError(
        'Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.',
        streamId,
        'start'
      );
    }

    streamRetryCount.set(streamId, 0);

    // Check if stream is already active
    if (activeStreams.has(streamId)) {
      addStreamLog(streamId, `[Error] Attempted to start an already active stream.`);
      throw createStreamingError('Stream is already active', streamId, 'start');
    }

    // Get stream from database
    const stream = await Stream.findById(streamId);
    if (!stream) {
      throw createNotFoundError('Stream', streamId);
    }

    // Immediately update status to 'starting' to prevent race conditions
    await Stream.updateStatus(streamId, 'starting', stream.user_id);
    addStreamLog(streamId, `[Status] Stream status set to 'starting'.`);

    // Validate stream configuration
    if (!stream.rtmp_url) {
      throw createValidationError('RTMP URL is required', null, 'rtmp_url');
    }

    if (!stream.stream_key) {
      throw createValidationError('Stream key is required', null, 'stream_key');
    }

    if (!stream.video_id) {
      throw createValidationError('Video is required for streaming', null, 'video_id');
    }
    const ffmpegArgs = await buildFFmpegArgs(stream);

    // Add CPU allocation for streaming
    const cpuAllocatedArgs = cpuManager.addStreamingCPUAllocation([...ffmpegArgs]);

    const fullCommand = `${ffmpegPath} ${cpuAllocatedArgs.join(' ')}`;
    addStreamLog(streamId, `Starting stream with command: ${fullCommand}`);

    // Log CPU allocation info
    const cpuInfo = cpuManager.getAllocationInfo();
    addStreamLog(streamId, `[CPU] Using ${cpuInfo.streaming.threads} threads on cores ${cpuInfo.streaming.range} for streaming`);

    console.log(`Starting stream: ${fullCommand}`);

    // Debug: Test FFmpeg executable first
    console.log(`[DEBUG] FFmpeg path: ${ffmpegPath}`);
    console.log(`[DEBUG] FFmpeg args: ${JSON.stringify(cpuAllocatedArgs)}`);

    let ffmpegProcess;
    try {
      ffmpegProcess = cpuManager.spawnWithCPUAffinity(ffmpegPath, cpuAllocatedArgs, {
        detached: false,
        stdio: ['ignore', 'pipe', 'pipe']
      }, 'streaming');

      console.log(`[DEBUG] FFmpeg process spawned successfully, PID: ${ffmpegProcess.pid}`);
      addStreamLog(streamId, `[DEBUG] FFmpeg process spawned, PID: ${ffmpegProcess.pid}`);

    } catch (spawnError) {
      console.error(`[DEBUG] Failed to spawn FFmpeg process: ${spawnError.message}`);
      addStreamLog(streamId, `[ERROR] Failed to spawn FFmpeg: ${spawnError.message}`);
      throw spawnError;
    }

    // Store the process in activeStreams with start timestamp
    ffmpegProcess.startTime = Date.now();

    // Detect if this is a YouTube stream for enhanced protection
    ffmpegProcess.isYouTube = stream.platform === 'YouTube' ||
                              stream.rtmp_url.includes('youtube.com') ||
                              stream.rtmp_url.includes('rtmp.youtube.com');

    if (ffmpegProcess.isYouTube) {
      addStreamLog(streamId, `[YouTube] Detected YouTube stream - applying enhanced stability protection`);
    }

    activeStreams.set(streamId, ffmpegProcess);

    // Add immediate error detection
    ffmpegProcess.on('error', (error) => {
      console.error(`[DEBUG] FFmpeg process error immediately after spawn: ${error.message}`);
      addStreamLog(streamId, `[ERROR] Immediate process error: ${error.message}`);
    });

    // Check if process is still alive after a brief moment
    setTimeout(() => {
      if (ffmpegProcess.killed || ffmpegProcess.exitCode !== null) {
        console.error(`[DEBUG] FFmpeg process died immediately - killed: ${ffmpegProcess.killed}, exitCode: ${ffmpegProcess.exitCode}`);
        addStreamLog(streamId, `[ERROR] Process died immediately - killed: ${ffmpegProcess.killed}, exitCode: ${ffmpegProcess.exitCode}`);
      } else {
        console.log(`[DEBUG] FFmpeg process still alive after 1 second, PID: ${ffmpegProcess.pid}`);
        addStreamLog(streamId, `[DEBUG] Process alive after 1s, PID: ${ffmpegProcess.pid}`);
      }
    }, 1000);

    // Don't update status to 'live' immediately - wait for FFmpeg to initialize
    addStreamLog(streamId, `[Status] FFmpeg process started, waiting for initialization before updating status.`);

    // Set a flag to track if status has been updated
    let statusUpdated = false;
    let validationAttempts = 0;
    const maxValidationAttempts = 3;

    // Add stream start timestamp for better tracking
    ffmpegProcess.streamStartTime = Date.now();

    // Setup stuck status detection for this stream
    setupStuckStatusDetection(streamId);

    // Setup long-duration health monitoring for copy mode streams
    if (ffmpegArgs.includes('-c:v') && ffmpegArgs.includes('copy')) {
      setupLongDurationHealthCheck(streamId);
      setupFileHandleHealthMonitoring(streamId);
    }

    // Enhanced status update logic with retry mechanism
    const attemptStatusUpdate = async (attempt = 1) => {
      if (!activeStreams.has(streamId) || statusUpdated) {
        return; // Stream stopped or already updated
      }

      validationAttempts = attempt;
      addStreamLog(streamId, `[Status] Validation attempt ${attempt}/${maxValidationAttempts} for stream status update.`);

      if (validateStreamProcess(streamId)) {
        try {
          await Stream.updateStatus(streamId, 'live', stream.user_id);
          statusUpdated = true;
          addStreamLog(streamId, `[Status] Stream status updated to 'live' after validation (attempt ${attempt}).`);
          console.log(`✅ Stream ${streamId} status updated to 'live' after validation attempt ${attempt}`);

          // Update startup phase to indicate stream is now live
          updateStartupPhase(streamId, 'live');

          // Clean up stuck status detection since stream is now successfully live
          cleanupStuckStatusDetection(streamId);
          addStreamLog(streamId, `[Status] Stuck status detection cleaned up - stream is now live and stable.`);
          console.log(`✅ Stream ${streamId} stuck detection cleaned up - stream is live`);

          return;
        } catch (error) {
          addStreamLog(streamId, `[Status] Error updating status to 'live' on attempt ${attempt}: ${error.message}`);
          console.error(`❌ Stream ${streamId} status update failed on attempt ${attempt}:`, error.message);
        }
      } else {
        addStreamLog(streamId, `[Status] Stream validation failed on attempt ${attempt}.`);
        console.log(`⚠️ Stream ${streamId} validation failed on attempt ${attempt}`);
      }

      // Retry logic with progressive delays
      if (attempt < maxValidationAttempts && activeStreams.has(streamId)) {
        const nextDelay = attempt === 1 ? 5000 : 8000; // 5s for retry, 8s for final attempt
        addStreamLog(streamId, `[Status] Scheduling retry in ${nextDelay/1000} seconds (attempt ${attempt + 1}/${maxValidationAttempts}).`);

        const retryTimer = setTimeout(() => {
          attemptStatusUpdate(attempt + 1);
        }, nextDelay);

        // Store retry timer for cleanup
        if (!ffmpegProcess.statusUpdateTimers) {
          ffmpegProcess.statusUpdateTimers = [];
        }
        ffmpegProcess.statusUpdateTimers.push(retryTimer);
      } else if (attempt >= maxValidationAttempts) {
        addStreamLog(streamId, `[Status] All validation attempts exhausted. Stream may need manual status check.`);
        console.log(`⚠️ Stream ${streamId} failed all ${maxValidationAttempts} validation attempts`);
      }
    };

    // Start first validation attempt after 15 seconds (increased for better stability)
    const initialStatusUpdateTimer = setTimeout(() => {
      attemptStatusUpdate(1);
    }, 15000); // 15 seconds initial delay for better FFmpeg initialization

    // Store the timer so we can clear it if stream stops early
    ffmpegProcess.statusUpdateTimer = initialStatusUpdateTimer;
    // Send notification for stream started
    try {
      await notificationService.notifyStreamStarted(streamId, stream.user_id, stream.title);
    } catch (notifError) {
      console.error('Error sending stream start notification:', notifError);
    }
    // Production-optimized FFmpeg logging
    const isProduction = process.env.NODE_ENV === 'production';
    const enableVerboseFFmpegLogs = process.env.ENABLE_VERBOSE_FFMPEG_LOGS === 'true';

    ffmpegProcess.stdout.on('data', (data) => {
      const message = data.toString().trim();
      if (message && !isProduction) {
        addStreamLog(streamId, `[OUTPUT] ${message}`);
        if (enableVerboseFFmpegLogs) {
          console.debug(`[FFMPEG_STDOUT] ${streamId}: ${message}`);
        }
      }
    });

    ffmpegProcess.stderr.on('data', (data) => {
      const message = data.toString().trim();

      if (message) {
        // Always log to stream logs for debugging
        addStreamLog(streamId, `[FFmpeg] ${message}`);

        // Filter out progress messages and only log important events
        const isProgressMessage = message.includes('frame=') ||
                                 message.includes('fps=') ||
                                 message.includes('bitrate=') ||
                                 message.includes('time=') ||
                                 message.includes('speed=');

        const isImportantMessage = message.includes('error') ||
                                  message.includes('Error') ||
                                  message.includes('warning') ||
                                  message.includes('Warning') ||
                                  message.includes('failed') ||
                                  message.includes('Failed');

        // In production, only log errors and warnings
        if (isImportantMessage) {
          console.error(`[FFMPEG_STDERR] ${streamId}: ${message}`);

          // Track errors for file handle health monitoring
          trackFileHandleHealthError(streamId, message);
        } else if (!isProduction && !isProgressMessage && enableVerboseFFmpegLogs) {
          console.debug(`[FFMPEG_STDERR] ${streamId}: ${message}`);
        }

        // Enhanced error detection for invalid RTMP credentials and connection issues
        const criticalErrors = [
          'I/O error',
          'Connection refused',
          'Network is unreachable',
          'Invalid stream key',
          'Authentication failed',
          '403 Forbidden',
          '404 Not Found',
          'RTMP handshake failed',
          'Failed to connect',
          'Connection timed out',
          'Server returned 4',  // Covers 400, 401, 403, 404, etc.
          'Unauthorized',
          'Access denied',
          'Invalid URL',
          'Bad request',
          'Stream not found',
          'Invalid credentials',
          'Permission denied'
        ];

        const hasCriticalError = criticalErrors.some(error => message.includes(error));

        // Check for critical errors that should trigger immediate auto-stop
        if (hasCriticalError) {

          const autoStopDecision = shouldAutoStopStream(streamId, message);

          if (autoStopDecision.shouldStop) {
            console.log(`[StreamingService] Critical error detected, auto-stopping stream ${streamId}: ${autoStopDecision.reason}`);
            // Send notification for stream error (async wrapper)
            (async () => {
              try {
                const stream = await Stream.findById(streamId);
                if (stream) {
                  await notificationService.notifyStreamError(streamId, stream.user_id, stream.title, message);
                }
              } catch (notifError) {
                console.error('Error sending stream error notification:', notifError);
              }
            })();
            // Use setTimeout to avoid blocking the current stderr processing
            setTimeout(async () => {
              await autoStopStream(streamId, autoStopDecision.reason);
            }, 1000);
          }
        }
      }
    });
    ffmpegProcess.on('exit', async (code, signal) => {
      addStreamLog(streamId, `Stream ended with code ${code}, signal: ${signal}`);
      console.log(`[FFMPEG_EXIT] ${streamId}: Code=${code}, Signal=${signal}`);
      // Clear all status update timers if they exist
      if (ffmpegProcess.statusUpdateTimer) {
        clearTimeout(ffmpegProcess.statusUpdateTimer);
        addStreamLog(streamId, `[Status] Cleared main status update timer due to process exit.`);
      }

      // Clear any retry timers
      if (ffmpegProcess.statusUpdateTimers && Array.isArray(ffmpegProcess.statusUpdateTimers)) {
        ffmpegProcess.statusUpdateTimers.forEach((timer, index) => {
          clearTimeout(timer);
          addStreamLog(streamId, `[Status] Cleared retry timer ${index + 1} due to process exit.`);
        });
        ffmpegProcess.statusUpdateTimers = [];
      }

      // Clean up health check monitoring
      cleanupHealthCheckMonitoring(streamId);

      // Clean up file handle health monitoring
      cleanupFileHandleHealthMonitoring(streamId);

      // Immediately remove from active streams to prevent status inconsistency
      const wasActive = activeStreams.delete(streamId);
      const isManualStop = manuallyStoppingStreams.has(streamId);

      if (isManualStop) {
        console.log(`[StreamingService] Stream ${streamId} was manually stopped, not restarting`);
        // Clean up all stream data for manual stops
        cleanupStreamData(streamId);
        if (wasActive) {
          try {
            await Stream.updateStatus(streamId, 'offline');
            if (typeof schedulerService !== 'undefined' && schedulerService.cancelStreamTermination) {
              schedulerService.handleStreamStopped(streamId);
            }
          } catch (error) {
            console.error(`[StreamingService] Error updating stream status after manual stop: ${error.message}`);
          }
        }
        return;
      }
      if (signal === 'SIGSEGV') {
        const retryCount = streamRetryCount.get(streamId) || 0;
        if (retryCount < MAX_RETRY_ATTEMPTS) {
          streamRetryCount.set(streamId, retryCount + 1);
          console.log(`[StreamingService] FFmpeg crashed with SIGSEGV. Attempting restart #${retryCount + 1} for stream ${streamId}`);
          addStreamLog(streamId, `FFmpeg crashed with SIGSEGV. Attempting restart #${retryCount + 1}`);
          setTimeout(async () => {
            try {
              const streamInfo = await Stream.findById(streamId);
              if (streamInfo) {
                const result = await startStream(streamId);
                if (!result.success) {
                  console.error(`[StreamingService] Failed to restart stream: ${result.error}`);
                  await Stream.updateStatus(streamId, 'offline');
                }
              } else {
                console.error(`[StreamingService] Cannot restart stream ${streamId}: not found in database`);
              }
            } catch (error) {
              console.error(`[StreamingService] Error during stream restart: ${error.message}`);
              try {
                await Stream.updateStatus(streamId, 'offline');
              } catch (dbError) {
                console.error(`Error updating stream status: ${dbError.message}`);
              }
            }
          }, 3000);
          return;
        } else {
          console.error(`[StreamingService] Maximum retry attempts (${MAX_RETRY_ATTEMPTS}) reached for stream ${streamId}`);
          addStreamLog(streamId, `Maximum retry attempts (${MAX_RETRY_ATTEMPTS}) reached, stopping stream`);
        }
      }
      else {
        let errorMessage = '';
        if (code !== 0 && code !== null) {
          errorMessage = `FFmpeg process exited with error code ${code}`;
          addStreamLog(streamId, errorMessage);
          console.error(`[StreamingService] ${errorMessage} for stream ${streamId}`);

          // Check if stream should be auto-stopped due to repeated failures
          const autoStopDecision = shouldAutoStopStream(streamId, errorMessage);

          if (autoStopDecision.shouldStop) {
            console.log(`[StreamingService] Auto-stopping stream ${streamId}: ${autoStopDecision.reason}`);
            await autoStopStream(streamId, autoStopDecision.reason);
            return;
          }

          const retryCount = streamRetryCount.get(streamId) || 0;
          if (retryCount < MAX_RETRY_ATTEMPTS) {
            streamRetryCount.set(streamId, retryCount + 1);
            console.log(`[StreamingService] FFmpeg exited with code ${code}. Attempting restart #${retryCount + 1} for stream ${streamId}`);
            setTimeout(async () => {
              try {
                const streamInfo = await Stream.findById(streamId);
                if (streamInfo) {
                  const result = await startStream(streamId);
                  if (!result.success) {
                    console.error(`[StreamingService] Failed to restart stream: ${result.error}`);
                    await Stream.updateStatus(streamId, 'offline');
                  }
                }
              } catch (error) {
                console.error(`[StreamingService] Error during stream restart: ${error.message}`);
                await Stream.updateStatus(streamId, 'offline');
              }
            }, 3000);
            return;
          } else {
            // Max retries reached, auto-stop the stream
            console.error(`[StreamingService] Maximum retry attempts (${MAX_RETRY_ATTEMPTS}) reached for stream ${streamId}`);
            await autoStopStream(streamId, `Maximum retry attempts reached (${MAX_RETRY_ATTEMPTS})`);
          }
        }
        if (wasActive) {
          try {
            // Get stream info for proper status update with user_id
            const stream = await Stream.findById(streamId);
            console.log(`[StreamingService] Updating stream ${streamId} status to offline after FFmpeg exit`);
            if (stream) {
              await Stream.updateStatus(streamId, 'offline', stream.user_id);
              console.log(`[StreamingService] ✅ Successfully updated stream ${streamId} status to offline`);
            } else {
              console.log(`[StreamingService] ⚠️ Stream ${streamId} not found in database, cannot update status`);
            }

            if (typeof schedulerService !== 'undefined' && schedulerService.handleStreamStopped) {
              schedulerService.handleStreamStopped(streamId);
            }
          } catch (error) {
            console.error(`[StreamingService] ❌ Error updating stream status after exit: ${error.message}`);
          }
        }
      }
    });
    ffmpegProcess.on('error', async (err) => {
      addStreamLog(streamId, `Error in stream process: ${err.message}`);
      console.error(`[FFMPEG_PROCESS_ERROR] ${streamId}: ${err.message}`);

      // Clear all status update timers on error
      if (ffmpegProcess.statusUpdateTimer) {
        clearTimeout(ffmpegProcess.statusUpdateTimer);
        addStreamLog(streamId, `[Status] Cleared main status update timer due to process error.`);
      }

      if (ffmpegProcess.statusUpdateTimers && Array.isArray(ffmpegProcess.statusUpdateTimers)) {
        ffmpegProcess.statusUpdateTimers.forEach((timer, index) => {
          clearTimeout(timer);
          addStreamLog(streamId, `[Status] Cleared retry timer ${index + 1} due to process error.`);
        });
        ffmpegProcess.statusUpdateTimers = [];
      }

      // Immediately remove from active streams to prevent status inconsistency
      activeStreams.delete(streamId);

      // Clean up stream data on error
      cleanupStreamData(streamId);

      try {
        // Get stream info for proper status update with user_id
        const stream = await Stream.findById(streamId);
        console.log(`[StreamingService] Updating stream ${streamId} status to offline after process error`);
        if (stream) {
          await Stream.updateStatus(streamId, 'offline', stream.user_id);
          console.log(`[StreamingService] ✅ Successfully updated stream ${streamId} status to offline after error`);
        } else {
          console.log(`[StreamingService] ⚠️ Stream ${streamId} not found in database during error handling`);
        }
      } catch (error) {
        console.error(`[StreamingService] ❌ Error updating stream status after process error: ${error.message}`);
      }
    });
    // Removed ffmpegProcess.unref() to ensure proper process management
    if (stream.duration && typeof schedulerService !== 'undefined') {
      schedulerService.scheduleStreamTermination(streamId, stream.duration);
    }
    return {
      success: true,
      message: 'Stream started successfully',
      isAdvancedMode: stream.use_advanced_settings
    };
  } catch (error) {
    // Enhanced error logging with context
    const errorContext = {
      streamId,
      operation: 'startStream',
      timestamp: new Date().toISOString()
    };

    logError(error, null, errorContext);
    addStreamLog(streamId, `Failed to start stream: ${error.message}`);

    // Clean up any partial state
    activeStreams.delete(streamId);
    streamRetryCount.delete(streamId);

    // Return structured error response
    return {
      success: false,
      error: error.message,
      errorType: error.type || 'STREAMING_ERROR',
      errorId: error.errorId
    };
  }
}
async function stopStream(streamId) {
  try {
    const ffmpegProcess = activeStreams.get(streamId);
    const isActive = ffmpegProcess !== undefined;
    console.log(`[StreamingService] Stop request for stream ${streamId}, isActive: ${isActive}`);
    if (!isActive) {
      const stream = await Stream.findById(streamId);
      if (stream && stream.status === 'live') {
        console.log(`[StreamingService] Stream ${streamId} not active in memory but status is 'live' in DB. Fixing status.`);
        await Stream.updateStatus(streamId, 'offline', stream.user_id);
        if (typeof schedulerService !== 'undefined' && schedulerService.cancelStreamTermination) {
          schedulerService.handleStreamStopped(streamId);
        }
        return { success: true, message: 'Stream status fixed (was not active but marked as live)' };
      }
      return { success: false, error: 'Stream is not active' };
    }
    addStreamLog(streamId, 'Stopping stream...');
    console.log(`[StreamingService] Stopping active stream ${streamId}`);
    manuallyStoppingStreams.add(streamId);

    // Enhanced process termination with timeout and fallback to SIGKILL
    try {
      console.log(`[StreamingService] Sending SIGTERM to FFmpeg process for stream ${streamId}`);
      addStreamLog(streamId, `[Stop] Sending graceful termination signal to FFmpeg process`);
      ffmpegProcess.kill('SIGTERM');

      // Set a timeout to force kill if SIGTERM doesn't work - increased timeout for better stability
      const forceKillTimeout = setTimeout(() => {
        if (activeStreams.has(streamId)) {
          console.log(`[StreamingService] SIGTERM timeout, sending SIGKILL to FFmpeg process for stream ${streamId}`);
          addStreamLog(streamId, `[Stop] Graceful termination timeout, forcing process termination`);
          try {
            ffmpegProcess.kill('SIGKILL');
          } catch (forceKillError) {
            console.error(`[StreamingService] Error force killing FFmpeg process: ${forceKillError.message}`);
            addStreamLog(streamId, `[Stop] Error during force termination: ${forceKillError.message}`);
          }
        }
      }, 10000); // Increased from 5 to 10 seconds timeout for better stability

      // Clear timeout if process exits normally
      ffmpegProcess.once('exit', () => {
        clearTimeout(forceKillTimeout);
        addStreamLog(streamId, `[Stop] FFmpeg process terminated successfully`);
      });

    } catch (killError) {
      console.error(`[StreamingService] Error killing FFmpeg process: ${killError.message}`);
      addStreamLog(streamId, `[Stop] Error during termination: ${killError.message}`);
      manuallyStoppingStreams.delete(streamId);
    }
    const stream = await Stream.findById(streamId);
    activeStreams.delete(streamId);
    if (stream) {
      await Stream.updateStatus(streamId, 'offline', stream.user_id);
      const updatedStream = await Stream.findById(streamId);
      await saveStreamHistory(updatedStream);

      // Send notification for stream stopped
      try {
        await notificationService.notifyStreamStopped(streamId, stream.user_id, stream.title);
      } catch (notifError) {
        console.error('Error sending stream stop notification:', notifError);
      }
    }
    if (typeof schedulerService !== 'undefined' && schedulerService.cancelStreamTermination) {
      schedulerService.handleStreamStopped(streamId);
    }
    return { success: true, message: 'Stream stopped successfully' };
  } catch (error) {
    manuallyStoppingStreams.delete(streamId);
    console.error(`[StreamingService] Error stopping stream ${streamId}:`, error);
    return { success: false, error: error.message };
  }
}
async function syncStreamStatuses() {
  try {
    console.log('[StreamingService] Syncing stream statuses...');
    const liveStreams = await Stream.findAll(null, 'live');
    let fixedStreams = 0;
    let validatedStreams = 0;

    // First pass: Check streams marked as 'live' in database
    for (const stream of liveStreams) {
      const isInMemory = activeStreams.has(stream.id);

      if (!isInMemory) {
        // Stream is marked live in DB but not in memory - definitely inconsistent
        console.log(`[StreamingService] INCONSISTENT: Stream ${stream.id} marked 'live' in DB but not in memory`);
        try {
          await Stream.updateStatus(stream.id, 'offline', stream.user_id);
          console.log(`[StreamingService] ✅ Fixed: Updated stream ${stream.id} status to 'offline'`);
          fixedStreams++;
        } catch (error) {
          console.error(`[StreamingService] ❌ Error updating stream ${stream.id} to offline:`, error.message);
        }
      } else {
        // Stream is in memory, validate if process is actually running
        const isReallyActive = validateStreamProcess(stream.id);

        if (!isReallyActive) {
          // Process is dead but still in memory - this should have been cleaned up by validateStreamProcess
          console.log(`[StreamingService] INCONSISTENT: Stream ${stream.id} in memory but process is dead`);
          try {
            await Stream.updateStatus(stream.id, 'offline', stream.user_id);
            console.log(`[StreamingService] ✅ Fixed: Updated dead stream ${stream.id} status to 'offline'`);
            fixedStreams++;
          } catch (error) {
            console.error(`[StreamingService] ❌ Error updating dead stream ${stream.id} to offline:`, error.message);
          }
        } else {
          validatedStreams++;
        }
      }
    }

    // Second pass: Check streams in memory but not marked as 'live' in database
    const activeStreamIds = Array.from(activeStreams.keys());
    for (const streamId of activeStreamIds) {
      // First validate the process is still running
      const isValidProcess = validateStreamProcess(streamId);

      if (!isValidProcess) {
        console.log(`[StreamingService] Process ${streamId} is no longer valid, already cleaned up by validateStreamProcess`);
        continue;
      }

      try {
        const stream = await Stream.findById(streamId);
        if (!stream) {
          console.log(`[StreamingService] ORPHANED: Stream ${streamId} active in memory but not found in DB`);
          const process = activeStreams.get(streamId);
          if (process) {
            try {
              console.log(`[StreamingService] Killing orphaned process for stream ${streamId}`);
              process.kill('SIGTERM');
              setTimeout(() => {
                if (!process.killed) {
                  console.log(`[StreamingService] Force killing orphaned process for stream ${streamId}`);
                  process.kill('SIGKILL');
                }
              }, 5000);
            } catch (error) {
              console.error(`[StreamingService] Error killing orphaned process ${streamId}:`, error.message);
            }
          }
          activeStreams.delete(streamId);
          cleanupStreamData(streamId);
        } else if (stream.status !== 'live') {
          console.log(`[StreamingService] INCONSISTENT: Stream ${streamId} active in memory but status is '${stream.status}' in DB`);
          try {
            await Stream.updateStatus(streamId, 'live', stream.user_id);
            console.log(`[StreamingService] ✅ Fixed: Updated stream ${streamId} status to 'live'`);
            fixedStreams++;
          } catch (error) {
            console.error(`[StreamingService] ❌ Error updating stream ${streamId} to live:`, error.message);
          }
        }
      } catch (error) {
        console.error(`[StreamingService] Error checking stream ${streamId}:`, error.message);
      }
    }

    const finalActiveCount = Array.from(activeStreams.keys()).length;
    console.log(`[StreamingService] ✅ Stream status sync completed:`);
    console.log(`   - Active streams in memory: ${finalActiveCount}`);
    console.log(`   - Validated streams: ${validatedStreams}`);
    console.log(`   - Fixed inconsistencies: ${fixedStreams}`);
    // Log current active streams for debugging
    if (finalActiveCount > 0) {
      const activeIds = Array.from(activeStreams.keys());
      console.log(`   - Active stream IDs: ${activeIds.join(', ')}`);
    }
  } catch (error) {
    console.error('[StreamingService] ❌ Error syncing stream statuses:', error);
  }
}

// Gentle initial sync after 30 seconds delay
console.log('[StreamingService] Scheduling gentle initial sync in 30 seconds...');
setTimeout(() => {
  syncStreamStatuses().then(() => {
    console.log('[StreamingService] Initial gentle sync completed');
  }).catch(error => {
    console.error('[StreamingService] Error during initial sync:', error);
  });
}, 30000); // 30 second delay

// Start periodic memory cleanup and health checks
console.log('[StreamingService] Starting periodic memory cleanup...');
const cleanupTimer = setInterval(async () => {
  try {
    performPeriodicCleanup();
    await performStreamHealthCheck();
  } catch (error) {
    console.error('[StreamingService] Error during periodic cleanup:', error);
  }
}, CLEANUP_INTERVAL);

// Ensure cleanup timer doesn't prevent process exit
cleanupTimer.unref();

// Balanced sync every 5 minutes to maintain accuracy without conflicts
setInterval(syncStreamStatuses, 5 * 60 * 1000); // 5 minutes
console.log('[StreamingService] ✅ Balanced sync enabled (5 minute intervals)');
function isStreamActive(streamId) {
  return activeStreams.has(streamId);
}

// Enhanced function to validate if a stream is actually running with graduated validation
function validateStreamProcess(streamId) {
  const process = activeStreams.get(streamId);
  if (!process) {
    console.log(`[StreamingService] 🔍 Process ${streamId} not found in activeStreams`);
    return false;
  }

  try {
    // Basic validation - check if process is still alive
    if (process.killed) {
      console.log(`[StreamingService] 💀 Process ${streamId} is marked as killed`);
      activeStreams.delete(streamId);
      cleanupStreamData(streamId);
      return false;
    }

    if (process.exitCode !== null) {
      console.log(`[StreamingService] 🚪 Process ${streamId} has exit code: ${process.exitCode}`);
      activeStreams.delete(streamId);
      cleanupStreamData(streamId);
      return false;
    }

    // Check if process has a valid PID
    if (!process.pid) {
      console.log(`[StreamingService] 🆔 Process ${streamId} has no PID`);
      activeStreams.delete(streamId);
      cleanupStreamData(streamId);
      return false;
    }

    // Enhanced graduated validation based on process age
    const processStartTime = process.streamStartTime || process.startTime || Date.now();
    const timeSinceStart = Date.now() - processStartTime;

    // Phase 1: Very early startup (0-5 seconds) - Very lenient
    if (timeSinceStart < 5000) {
      // Just check if process exists and hasn't been killed
      const isBasicallyAlive = !process.killed && process.exitCode === null && process.pid;
      if (isBasicallyAlive) {
        // console.log(`[StreamingService] ✅ Process ${streamId} in early startup phase (${Math.round(timeSinceStart/1000)}s) - basic validation passed`); // Removed for production
      }
      return isBasicallyAlive;
    }

    // Phase 2: Early startup (5-15 seconds) - Lenient with additional checks
    if (timeSinceStart < 15000) {
      const isHealthy = !process.killed && process.exitCode === null && process.pid;

      // Additional check: ensure process hasn't been restarted multiple times
      const hasReasonableUptime = timeSinceStart >= 3000; // At least 3 seconds uptime

      if (isHealthy && hasReasonableUptime) {
        // console.log(`[StreamingService] ✅ Process ${streamId} in startup phase (${Math.round(timeSinceStart/1000)}s) - health checks passed`); // Removed for production
        return true;
      } else if (isHealthy) {
        // Still healthy but very new, give benefit of doubt
        // console.log(`[StreamingService] ⏳ Process ${streamId} very new (${Math.round(timeSinceStart/1000)}s) - giving benefit of doubt`); // Removed for production
        return true;
      }
      return false;
    }

    // Phase 3: Stabilization period (15-30 seconds) - More thorough validation
    if (timeSinceStart < 30000) {
      const isHealthy = !process.killed && process.exitCode === null && process.pid;

      // Check if process has been stable (no recent restarts)
      const isStable = timeSinceStart >= 10000; // At least 10 seconds of continuous running

      if (isHealthy && isStable) {
        // console.log(`[StreamingService] ✅ Process ${streamId} in stabilization phase (${Math.round(timeSinceStart/1000)}s) - stability validated`); // Removed for production
        return true;
      } else if (isHealthy) {
        // Healthy but might be restarting frequently - be cautious but not too strict
        // console.log(`[StreamingService] ⚠️ Process ${streamId} healthy but potentially unstable (${Math.round(timeSinceStart/1000)}s)`); // Removed for production
        return true; // Still give benefit of doubt during stabilization
      }
      return false;
    }

    // Phase 4: Mature process (30+ seconds) - Full validation
    const isHealthy = !process.killed && process.exitCode === null && process.pid;

    if (isHealthy) {
      // For mature processes, we can add more sophisticated checks here if needed
      // For now, basic health check is sufficient
      // console.log(`[StreamingService] ✅ Process ${streamId} mature and healthy (${Math.round(timeSinceStart/1000)}s)`); // Removed for production
      return true;
    }

    // console.log(`[StreamingService] ❌ Process ${streamId} failed health check after ${Math.round(timeSinceStart/1000)}s`); // Removed for production
    return false;

  } catch (error) {
    console.error(`[StreamingService] ❌ Error validating process ${streamId}:`, error.message);
    // Don't automatically delete on error during validation - might be temporary
    // Only return false to indicate validation failed, but don't clean up
    return false;
  }
}

// Function to force cleanup orphaned FFmpeg processes
async function forceCleanupOrphanedProcesses() {
  const { exec } = require('child_process');
  const os = require('os');

  try {
    console.log('[StreamingService] Checking for orphaned FFmpeg processes...');
    if (os.platform() === 'win32') {
      // Windows - check for FFmpeg processes
      exec('tasklist /FI "IMAGENAME eq ffmpeg.exe" /FO CSV', (error, stdout) => {
        if (!error && stdout) {
          const lines = stdout.split('\n');
          const ffmpegProcesses = lines.filter(line => line.includes('ffmpeg.exe'));

          if (ffmpegProcesses.length > 1) { // More than header line
            console.log(`[StreamingService] Found ${ffmpegProcesses.length - 1} FFmpeg processes, cleaning up...`);
            exec('taskkill /F /IM ffmpeg.exe', (killError) => {
              if (killError) {
                console.error('[StreamingService] Error killing orphaned FFmpeg processes:', killError.message);
              } else {
                console.log('[StreamingService] Orphaned FFmpeg processes cleaned up');
              }
            });
          }
        }
      });
    } else {
      // Linux/Mac - check for FFmpeg processes
      exec('pgrep -f ffmpeg', (error, stdout) => {
        if (!error && stdout) {
          const pids = stdout.trim().split('\n').filter(pid => pid);
          if (pids.length > 0) {
            console.log(`[StreamingService] Found ${pids.length} FFmpeg processes, cleaning up...`);
            exec(`kill -9 ${pids.join(' ')}`, (killError) => {
              if (killError) {
                console.error('[StreamingService] Error killing orphaned FFmpeg processes:', killError.message);
              } else {
                console.log('[StreamingService] Orphaned FFmpeg processes cleaned up');
              }
            });
          }
        }
      });
    }
  } catch (error) {
    console.error('[StreamingService] Error during orphaned process cleanup:', error);
  }
}
function getActiveStreams() {
  return Array.from(activeStreams.keys());
}
function getStreamLogs(streamId) {
  const logs = streamLogs.get(streamId) || [];
  // Ensure all logs are strings to prevent "[object Object]" display
  return logs.map(log => typeof log === 'string' ? log : JSON.stringify(log));
}

// Function to track stream failures
function trackStreamFailure(streamId, errorMessage) {
  const now = Date.now();

  if (!streamFailureTimestamps.has(streamId)) {
    streamFailureTimestamps.set(streamId, []);
  }

  const failures = streamFailureTimestamps.get(streamId);
  failures.push({ timestamp: now, error: errorMessage });

  // Keep only failures within the time window
  const windowStart = now - (AUTO_STOP_CONFIG.FAILURE_WINDOW_MINUTES * 60 * 1000);
  const recentFailures = failures.filter(f => f.timestamp >= windowStart);
  streamFailureTimestamps.set(streamId, recentFailures);

  return recentFailures;
}

// Function to detect and handle stuck status during startup
function setupStuckStatusDetection(streamId) {
  // Clear any existing timer
  if (stuckStatusTimers.has(streamId)) {
    clearTimeout(stuckStatusTimers.get(streamId));
  }

  // Initialize startup phase tracking
  streamStartupPhases.set(streamId, {
    phase: 'initializing',
    startTime: Date.now(),
    phaseStartTime: Date.now()
  });

  // Set up periodic stuck status checking
  const checkStuckStatus = async () => {
    if (!activeStreams.has(streamId)) {
      // Stream stopped, cleanup
      stuckStatusTimers.delete(streamId);
      streamStartupPhases.delete(streamId);
      return;
    }

    const phaseInfo = streamStartupPhases.get(streamId);
    if (!phaseInfo) return;

    // Check if stream is already live - if so, stop stuck detection
    try {
      const stream = await Stream.findById(streamId);
      if (stream && stream.status === 'live') {
        addStreamLog(streamId, `[StuckDetection] Stream is already live, cleaning up stuck detection.`);
        console.log(`✅ Stream ${streamId} is live, cleaning up stuck detection`);
        cleanupStuckStatusDetection(streamId);
        return;
      }
    } catch (error) {
      console.error(`[StuckDetection] Error checking stream status for ${streamId}:`, error.message);
    }

    const now = Date.now();
    const totalStartupTime = now - phaseInfo.startTime;
    const currentPhaseTime = now - phaseInfo.phaseStartTime;

    // Apply startup grace period - don't check for stuck status during initial startup
    if (totalStartupTime < STUCK_STATUS_CONFIG.STARTUP_GRACE_PERIOD) {
      addStreamLog(streamId, `[StuckDetection] In startup grace period (${Math.round(totalStartupTime/1000)}s/${Math.round(STUCK_STATUS_CONFIG.STARTUP_GRACE_PERIOD/1000)}s) - skipping stuck check`);
      // Schedule next check
      const timer = setTimeout(checkStuckStatus, STUCK_STATUS_CONFIG.STUCK_CHECK_INTERVAL);
      stuckStatusTimers.set(streamId, timer);
      return;
    }

    // Check if stream is stuck in current phase
    let isStuck = false;
    let stuckReason = '';

    // If stream is in 'live' phase, it's no longer in startup - clean up stuck detection
    if (phaseInfo.phase === 'live') {
      addStreamLog(streamId, `[StuckDetection] Stream is in live phase, cleaning up stuck detection.`);
      console.log(`✅ Stream ${streamId} is in live phase, cleaning up stuck detection`);
      cleanupStuckStatusDetection(streamId);
      return;
    }

    switch (phaseInfo.phase) {
      case 'initializing':
        if (currentPhaseTime > STUCK_STATUS_CONFIG.MAX_INITIALIZING_TIME) {
          isStuck = true;
          stuckReason = `Stream stuck in initializing phase for ${Math.round(currentPhaseTime/1000)}s`;
        }
        break;
      case 'connecting':
        if (currentPhaseTime > STUCK_STATUS_CONFIG.MAX_CONNECTING_TIME) {
          isStuck = true;
          stuckReason = `Stream stuck in connecting phase for ${Math.round(currentPhaseTime/1000)}s`;
        }
        break;
      case 'buffering':
        if (currentPhaseTime > STUCK_STATUS_CONFIG.MAX_BUFFERING_TIME) {
          isStuck = true;
          stuckReason = `Stream stuck in buffering phase for ${Math.round(currentPhaseTime/1000)}s`;
        }
        break;
      case 'stabilizing':
        if (currentPhaseTime > STUCK_STATUS_CONFIG.MAX_STABILIZING_TIME) {
          isStuck = true;
          stuckReason = `Stream stuck in stabilizing phase for ${Math.round(currentPhaseTime/1000)}s`;
        }
        break;
    }

    // Check absolute startup timeout - but only if we're still in startup phases
    if (totalStartupTime > STUCK_STATUS_CONFIG.ABSOLUTE_STARTUP_TIMEOUT) {
      // Additional check: if stream has been running for a very long time (>10 minutes),
      // it's probably stable and we should stop stuck detection rather than mark as stuck
      if (totalStartupTime > 10 * 60 * 1000) { // 10 minutes
        addStreamLog(streamId, `[StuckDetection] Stream has been running for ${Math.round(totalStartupTime/1000)}s - assuming stable, cleaning up stuck detection.`);
        console.log(`✅ Stream ${streamId} has been running for ${Math.round(totalStartupTime/1000)}s - cleaning up stuck detection`);
        cleanupStuckStatusDetection(streamId);
        return;
      }

      isStuck = true;
      stuckReason = `Stream exceeded absolute startup timeout (${Math.round(totalStartupTime/1000)}s)`;
    }

    if (isStuck) {
      addStreamLog(streamId, `[StuckDetection] ${stuckReason} - marking as error`);
      console.error(`[StreamingService] Stuck status detected for stream ${streamId}: ${stuckReason}`);

      // Mark stream as error and stop it
      autoStopStream(streamId, `Stuck in startup: ${stuckReason}`);
      return;
    }

    // Schedule next check
    const timer = setTimeout(checkStuckStatus, STUCK_STATUS_CONFIG.STUCK_CHECK_INTERVAL);
    stuckStatusTimers.set(streamId, timer);
  };

  // Start the first check
  const timer = setTimeout(checkStuckStatus, STUCK_STATUS_CONFIG.STUCK_CHECK_INTERVAL);
  stuckStatusTimers.set(streamId, timer);
}

// Setup long-duration health monitoring for copy mode streams
function setupLongDurationHealthCheck(streamId) {
  // Start health checks after the stream has been running for a while
  const startTimer = setTimeout(() => {
    if (!activeStreams.has(streamId)) {
      return; // Stream no longer active
    }

    addStreamLog(streamId, `[HealthCheck] Starting long-duration health monitoring`);

    let consecutiveFailures = 0;
    let lastOutputTime = Date.now();

    const performHealthCheck = () => {
      if (!activeStreams.has(streamId)) {
        return; // Stream stopped, cleanup
      }

      const ffmpegProcess = activeStreams.get(streamId);
      const currentTime = Date.now();
      const timeSinceLastOutput = currentTime - lastOutputTime;

      // Check if process is still alive and responsive
      const isProcessAlive = ffmpegProcess && !ffmpegProcess.killed && ffmpegProcess.exitCode === null;

      if (!isProcessAlive) {
        addStreamLog(streamId, `[HealthCheck] Process is not alive, stopping health monitoring`);
        return;
      }

      // Check for silence (no output for extended period)
      if (timeSinceLastOutput > HEALTH_CHECK_CONFIG.MAX_SILENCE_DURATION) {
        consecutiveFailures++;
        addStreamLog(streamId, `[HealthCheck] Silence detected: ${Math.round(timeSinceLastOutput/1000)}s since last output (failure ${consecutiveFailures}/${HEALTH_CHECK_CONFIG.MAX_HEALTH_FAILURES})`);

        if (consecutiveFailures >= HEALTH_CHECK_CONFIG.MAX_HEALTH_FAILURES) {
          addStreamLog(streamId, `[HealthCheck] Maximum health failures reached, restarting stream`);
          console.log(`[StreamingService] Health check failed for stream ${streamId}, restarting due to prolonged silence`);

          // Restart the stream
          restartStreamDueToHealthFailure(streamId, 'Prolonged silence detected in copy mode stream');
          return;
        }
      } else {
        // Reset failure count on successful check
        if (consecutiveFailures > 0) {
          addStreamLog(streamId, `[HealthCheck] Stream recovered, resetting failure count`);
          consecutiveFailures = 0;
        }
      }

      // Update last output time if we detect recent activity
      // This is a simplified check - in a real implementation you'd monitor actual output
      lastOutputTime = currentTime;

      // Schedule next health check
      const timer = setTimeout(performHealthCheck, HEALTH_CHECK_CONFIG.HEALTH_CHECK_INTERVAL);
      streamHealthTimers.set(streamId, timer);
    };

    // Start the health check cycle
    performHealthCheck();

  }, HEALTH_CHECK_CONFIG.HEALTH_CHECK_START_DELAY);

  streamHealthTimers.set(streamId, startTimer);
}

// Restart stream due to health check failure
async function restartStreamDueToHealthFailure(streamId, reason) {
  try {
    addStreamLog(streamId, `[HealthRestart] Restarting stream: ${reason}`);

    // Stop the current stream
    await stopStream(streamId);

    // Wait a moment before restarting
    setTimeout(async () => {
      try {
        const streamInfo = await Stream.findById(streamId);
        if (streamInfo && streamInfo.status !== 'offline') {
          const result = await startStream(streamId);
          if (result.success) {
            addStreamLog(streamId, `[HealthRestart] Stream successfully restarted after health failure`);
          } else {
            addStreamLog(streamId, `[HealthRestart] Failed to restart stream: ${result.error}`);
            await Stream.updateStatus(streamId, 'error');
          }
        }
      } catch (error) {
        console.error(`[StreamingService] Error during health restart: ${error.message}`);
        addStreamLog(streamId, `[HealthRestart] Error during restart: ${error.message}`);
        await Stream.updateStatus(streamId, 'error');
      }
    }, 5000); // 5 second delay before restart

  } catch (error) {
    console.error(`[StreamingService] Error in health restart: ${error.message}`);
    addStreamLog(streamId, `[HealthRestart] Error: ${error.message}`);
  }
}

// Function to update startup phase (called when status changes)
function updateStartupPhase(streamId, newPhase) {
  const phaseInfo = streamStartupPhases.get(streamId);
  if (phaseInfo && phaseInfo.phase !== newPhase) {
    phaseInfo.phase = newPhase;
    phaseInfo.phaseStartTime = Date.now();
    streamStartupPhases.set(streamId, phaseInfo);
    addStreamLog(streamId, `[StuckDetection] Phase updated to: ${newPhase}`);
  }
}

// Function to cleanup stuck status detection
function cleanupStuckStatusDetection(streamId) {
  if (stuckStatusTimers.has(streamId)) {
    clearTimeout(stuckStatusTimers.get(streamId));
    stuckStatusTimers.delete(streamId);
  }
  streamStartupPhases.delete(streamId);
}

// Cleanup health check monitoring for a stream
function cleanupHealthCheckMonitoring(streamId) {
  if (streamHealthTimers.has(streamId)) {
    clearTimeout(streamHealthTimers.get(streamId));
    streamHealthTimers.delete(streamId);
    addStreamLog(streamId, `[HealthCheck] Cleaned up health monitoring`);
  }
}

// Cleanup file handle health monitoring for a stream
function cleanupFileHandleHealthMonitoring(streamId) {
  // Clear health monitoring timer
  const healthTimerKey = streamId + '_filehandle';
  if (streamHealthTimers.has(healthTimerKey)) {
    clearTimeout(streamHealthTimers.get(healthTimerKey));
    streamHealthTimers.delete(healthTimerKey);
    addStreamLog(streamId, `[FileHandleHealth] Cleaned up file handle health monitoring`);
  }

  // Clear health data
  if (fileHandleHealthData.has(streamId)) {
    fileHandleHealthData.delete(streamId);
  }

  // Clear YouTube alert cooldown
  if (youtubeAlertCooldowns.has(streamId)) {
    youtubeAlertCooldowns.delete(streamId);
  }
}

// Function to analyze if stream should be auto-stopped - Enhanced with graduated thresholds and startup grace period
function shouldAutoStopStream(streamId, errorMessage) {
  const recentFailures = trackStreamFailure(streamId, errorMessage);

  // Get stream age and determine protection level
  const ffmpegProcess = activeStreams.get(streamId);
  let streamAge = 0;
  let isInStartupGrace = false;
  let isInEarlyStage = false;
  let errorType = 'runtime'; // Default to runtime error

  if (ffmpegProcess && ffmpegProcess.streamStartTime) {
    streamAge = Date.now() - ffmpegProcess.streamStartTime;
    isInStartupGrace = streamAge < AUTO_STOP_CONFIG.STARTUP_GRACE_PERIOD_MS;
    isInEarlyStage = streamAge < AUTO_STOP_CONFIG.EARLY_STAGE_DURATION_MS;

    // Classify error type based on stream age
    if (streamAge < AUTO_STOP_CONFIG.EARLY_STAGE_DURATION_MS) {
      errorType = 'startup';
    }
  }

  // Phase 4A: Startup Grace Period - No auto-stop during startup grace period
  if (isInStartupGrace) {
    addStreamLog(streamId, `[AutoStop] Stream in startup grace period (${Math.round(streamAge/1000)}s/${Math.round(AUTO_STOP_CONFIG.STARTUP_GRACE_PERIOD_MS/1000)}s), skipping auto-stop check`);
    return { shouldStop: false, reason: `Stream in startup grace period (${Math.round(streamAge/1000)}s)` };
  }

  addStreamLog(streamId, `[AutoStop] Stream age: ${Math.round(streamAge/1000)}s, Error type: ${errorType}, Stage: ${isInEarlyStage ? 'early' : 'mature'}`);

  // Phase 4A: Determine appropriate thresholds based on stream age and type
  let currentThresholds;
  let thresholdDescription;

  // Check if this is a YouTube stream for additional leniency
  const isYoutube = ffmpegProcess && ffmpegProcess.isYouTube;

  if (errorType === 'startup') {
    // Startup phase (0-2 minutes) - Very lenient thresholds
    currentThresholds = {
      MAX_CONSECUTIVE_FAILURES: isYoutube ? AUTO_STOP_CONFIG.STARTUP_MAX_CONSECUTIVE_FAILURES + 3 : AUTO_STOP_CONFIG.STARTUP_MAX_CONSECUTIVE_FAILURES,
      I_O_ERROR_THRESHOLD: isYoutube ? AUTO_STOP_CONFIG.STARTUP_I_O_ERROR_THRESHOLD + 2 : AUTO_STOP_CONFIG.STARTUP_I_O_ERROR_THRESHOLD,
      CONNECTION_ERROR_THRESHOLD: isYoutube ? AUTO_STOP_CONFIG.STARTUP_CONNECTION_ERROR_THRESHOLD + 2 : AUTO_STOP_CONFIG.STARTUP_CONNECTION_ERROR_THRESHOLD
    };
    thresholdDescription = isYoutube ? 'startup-YouTube' : 'startup';
  } else if (isInEarlyStage) {
    // Early stage (2-10 minutes) - Moderate thresholds
    currentThresholds = {
      MAX_CONSECUTIVE_FAILURES: isYoutube ? AUTO_STOP_CONFIG.EARLY_MAX_CONSECUTIVE_FAILURES + 2 : AUTO_STOP_CONFIG.EARLY_MAX_CONSECUTIVE_FAILURES,
      I_O_ERROR_THRESHOLD: isYoutube ? AUTO_STOP_CONFIG.EARLY_I_O_ERROR_THRESHOLD + 1 : AUTO_STOP_CONFIG.EARLY_I_O_ERROR_THRESHOLD,
      CONNECTION_ERROR_THRESHOLD: isYoutube ? AUTO_STOP_CONFIG.EARLY_CONNECTION_ERROR_THRESHOLD + 1 : AUTO_STOP_CONFIG.EARLY_CONNECTION_ERROR_THRESHOLD
    };
    thresholdDescription = isYoutube ? 'early-YouTube' : 'early';
  } else {
    // Mature stage (10+ minutes) - Normal thresholds
    currentThresholds = {
      MAX_CONSECUTIVE_FAILURES: isYoutube ? AUTO_STOP_CONFIG.MAX_CONSECUTIVE_FAILURES + 2 : AUTO_STOP_CONFIG.MAX_CONSECUTIVE_FAILURES,
      I_O_ERROR_THRESHOLD: isYoutube ? AUTO_STOP_CONFIG.I_O_ERROR_THRESHOLD + 1 : AUTO_STOP_CONFIG.I_O_ERROR_THRESHOLD,
      CONNECTION_ERROR_THRESHOLD: isYoutube ? AUTO_STOP_CONFIG.CONNECTION_ERROR_THRESHOLD + 1 : AUTO_STOP_CONFIG.CONNECTION_ERROR_THRESHOLD
    };
    thresholdDescription = isYoutube ? 'mature-YouTube' : 'mature';
  }

  addStreamLog(streamId, `[AutoStop] Using ${thresholdDescription} thresholds: failures=${currentThresholds.MAX_CONSECUTIVE_FAILURES}, I/O=${currentThresholds.I_O_ERROR_THRESHOLD}, connection=${currentThresholds.CONNECTION_ERROR_THRESHOLD}`);

  // Analyze error patterns with graduated thresholds
  const ioErrors = recentFailures.filter(f =>
    f.error.includes('I/O error') ||
    f.error.includes('Connection refused') ||
    f.error.includes('Network is unreachable')
  ).length;

  const connectionErrors = recentFailures.filter(f =>
    f.error.includes('Connection') ||
    f.error.includes('timeout') ||
    f.error.includes('refused')
  ).length;

  // Apply graduated thresholds
  if (ioErrors >= currentThresholds.I_O_ERROR_THRESHOLD) {
    addStreamLog(streamId, `[AutoStop] Too many I/O errors detected: ${ioErrors}/${currentThresholds.I_O_ERROR_THRESHOLD} (${thresholdDescription})`);
    return { shouldStop: true, reason: `Too many I/O errors (${ioErrors}/${currentThresholds.I_O_ERROR_THRESHOLD}) - ${thresholdDescription} threshold` };
  }

  if (connectionErrors >= currentThresholds.CONNECTION_ERROR_THRESHOLD) {
    addStreamLog(streamId, `[AutoStop] Too many connection errors detected: ${connectionErrors}/${currentThresholds.CONNECTION_ERROR_THRESHOLD} (${thresholdDescription})`);
    return { shouldStop: true, reason: `Too many connection errors (${connectionErrors}/${currentThresholds.CONNECTION_ERROR_THRESHOLD}) - ${thresholdDescription} threshold` };
  }

  if (recentFailures.length >= currentThresholds.MAX_CONSECUTIVE_FAILURES) {
    addStreamLog(streamId, `[AutoStop] Too many consecutive failures detected: ${recentFailures.length}/${currentThresholds.MAX_CONSECUTIVE_FAILURES} (${thresholdDescription})`);
    return { shouldStop: true, reason: `Too many consecutive failures (${recentFailures.length}/${currentThresholds.MAX_CONSECUTIVE_FAILURES}) - ${thresholdDescription} threshold` };
  }

  addStreamLog(streamId, `[AutoStop] Stream within acceptable limits (${thresholdDescription}): ${recentFailures.length} recent failures, ${ioErrors} I/O errors, ${connectionErrors} connection errors`);
  return { shouldStop: false, reason: `Stream within acceptable limits - ${thresholdDescription} thresholds` };
}

// Function to auto-stop a problematic stream
async function autoStopStream(streamId, reason) {
  try {
    console.log(`[StreamingService] Auto-stopping stream ${streamId}: ${reason}`);
    // Add to failed streams list
    failedStreams.add(streamId);

    // Stop the stream
    await stopStream(streamId);

    // Add log entry
    addStreamLog(streamId, `Stream auto-stopped: ${reason}`);
    addStreamLog(streamId, `Stream has been temporarily disabled. Check your RTMP settings and clear failed status to retry.`);

    // Update stream status to "error" instead of "offline"
    const stream = await Stream.findById(streamId);
    if (stream) {
      await Stream.updateStatus(streamId, 'error', stream.user_id, reason);
      console.log(`[StreamingService] Stream ${streamId} status set to 'error': ${reason}`);
    }

    // Clear retry count
    streamRetryCount.delete(streamId);

    // Remove from blacklist after 15 minutes
    setTimeout(() => {
      failedStreams.delete(streamId);
      streamFailureTimestamps.delete(streamId);
      console.log(`[StreamingService] Stream ${streamId} removed from failed streams list after cooldown`);
    }, 15 * 60 * 1000); // 15 minutes

    return true;
  } catch (error) {
    console.error(`[StreamingService] Error auto-stopping stream ${streamId}:`, error);
    return false;
  }
}

// Function to clear failed streams (for manual recovery)
async function clearFailedStream(streamId) {
  const wasBlacklisted = failedStreams.has(streamId);

  // Use centralized cleanup function
  cleanupStreamData(streamId);

  // Reset status from "error" to "offline" when clearing failed status
  try {
    const stream = await Stream.findById(streamId);
    if (stream && stream.status === 'error') {
      await Stream.updateStatus(streamId, 'offline', stream.user_id);
      console.log(`[StreamingService] Reset stream ${streamId} status from 'error' to 'offline'`);
    }
  } catch (error) {
    console.error(`[StreamingService] Error resetting stream status: ${error.message}`);
  }

  console.log(`[StreamingService] Cleared failed status for stream ${streamId}, was blacklisted: ${wasBlacklisted}`);
  return wasBlacklisted;
}

// Force stop function for stuck streams (bypasses graceful shutdown)
async function forceStopStream(streamId) {
  try {
    console.log(`[ForceStop] Starting force stop for stream ${streamId}`);
    addStreamLog(streamId, `[ForceStop] Force stopping stream initiated`);

    // Check if stream exists in database first
    const stream = await Stream.findById(streamId);
    if (!stream) {
      console.error(`[ForceStop] Stream ${streamId} not found in database`);

      // Even if stream doesn't exist in DB, try to clean up any running process
      const ffmpegProcess = activeStreams.get(streamId);
      if (ffmpegProcess) {
        console.log(`[ForceStop] Stream not in DB but process exists, cleaning up process`);
        try {
          if (ffmpegProcess.pid && !ffmpegProcess.killed) {
            ffmpegProcess.kill('SIGKILL');
            console.log(`[ForceStop] Killed orphaned process PID ${ffmpegProcess.pid}`);
          }
        } catch (killError) {
          console.error(`[ForceStop] Error killing orphaned process: ${killError.message}`);
        }
        activeStreams.delete(streamId);
        cleanupStreamData(streamId);
        return { success: true, message: 'Orphaned process cleaned up successfully' };
      }

      return { success: false, error: 'Stream not found in database' };
    }

    console.log(`[ForceStop] Stream ${streamId} found in database, current status: ${stream.status}`);
    const ffmpegProcess = activeStreams.get(streamId);
    console.log(`[ForceStop] FFmpeg process exists in memory: ${!!ffmpegProcess}`);
    if (ffmpegProcess) {
      console.log(`[ForceStop] Process details - PID: ${ffmpegProcess.pid}, killed: ${ffmpegProcess.killed}, exitCode: ${ffmpegProcess.exitCode}`);
      // Clear any status update timers immediately
      if (ffmpegProcess.statusUpdateTimer) {
        clearTimeout(ffmpegProcess.statusUpdateTimer);
        console.log(`[ForceStop] Cleared main status update timer`);
      }

      if (ffmpegProcess.statusUpdateTimers && Array.isArray(ffmpegProcess.statusUpdateTimers)) {
        ffmpegProcess.statusUpdateTimers.forEach(timer => clearTimeout(timer));
        ffmpegProcess.statusUpdateTimers = [];
        console.log(`[ForceStop] Cleared retry timers`);
      }

      // Force kill the process immediately (no graceful SIGTERM)
      try {
        if (ffmpegProcess.pid && !ffmpegProcess.killed) {
          ffmpegProcess.kill('SIGKILL');
          console.log(`[ForceStop] Sent SIGKILL to FFmpeg process PID ${ffmpegProcess.pid}`);
          addStreamLog(streamId, `[ForceStop] Sent SIGKILL to FFmpeg process PID ${ffmpegProcess.pid}`);
        } else {
          console.log(`[ForceStop] Process already killed or no PID, skipping SIGKILL`);
          addStreamLog(streamId, `[ForceStop] Process already killed or no PID, skipping SIGKILL`);
        }
      } catch (killError) {
        console.error(`[ForceStop] Error force killing FFmpeg process: ${killError.message}`);
        addStreamLog(streamId, `[ForceStop] Error during force kill: ${killError.message}`);
        // Don't return error here, continue with cleanup
      }

      // Remove from active streams immediately
      activeStreams.delete(streamId);
      console.log(`[ForceStop] Removed stream from active streams`);
    } else {
      console.log(`[ForceStop] No FFmpeg process found in memory for stream ${streamId}`);
      addStreamLog(streamId, `[ForceStop] No FFmpeg process found in memory`);
    }

    // Clean up all tracking data
    console.log(`[ForceStop] Starting cleanup of tracking data`);
    cleanupStreamData(streamId);
    console.log(`[ForceStop] Completed cleanup of tracking data`);
    // Update stream status to offline
    try {
      console.log(`[ForceStop] Updating stream status to offline`);
      await Stream.updateStatus(streamId, 'offline', stream.user_id, 'Force stopped by user');
      console.log(`[ForceStop] Successfully updated stream status to offline`);
      addStreamLog(streamId, `[ForceStop] Stream status updated to offline`);
    } catch (statusError) {
      console.error(`[ForceStop] Error updating stream status: ${statusError.message}`);
      addStreamLog(streamId, `[ForceStop] Error updating status: ${statusError.message}`);
      // Don't return error here, force stop was still successful
    }

    console.log(`[ForceStop] Stream ${streamId} force stopped successfully`);
    return { success: true, message: 'Stream force stopped successfully' };

  } catch (error) {
    console.error(`[ForceStop] Error force stopping stream ${streamId}:`, error);
    console.error(`[ForceStop] Error stack:`, error.stack);
    addStreamLog(streamId, `[ForceStop] Error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function saveStreamHistory(stream) {
  try {
    if (!stream.start_time) {
      console.log(`[StreamingService] Not saving history for stream ${stream.id} - no start time recorded`);
      return false;
    }
    const startTime = new Date(stream.start_time);
    const endTime = stream.end_time ? new Date(stream.end_time) : new Date();
    const durationSeconds = Math.floor((endTime - startTime) / 1000);
    if (durationSeconds < 5) { // Changed from 1 to 5 seconds minimum
      console.log(`[StreamingService] Not saving history for stream ${stream.id} - duration too short (${durationSeconds}s)`);
      return false;
    }

    // Get video details with error handling
    let videoDetails = null;
    if (stream.video_id) {
      try {
        videoDetails = await Video.findById(stream.video_id);
      } catch (error) {
        console.warn(`[StreamingService] Could not fetch video details for video_id ${stream.video_id}:`, error.message);
      }
    }

    const historyData = {
      id: uuidv4(),
      stream_id: stream.id,
      title: stream.title || 'Untitled Stream',
      platform: stream.platform || 'Custom',
      platform_icon: stream.platform_icon || 'ti-broadcast',
      video_id: stream.video_id,
      video_title: videoDetails ? videoDetails.title : null,
      resolution: stream.resolution || '1280x720',
      bitrate: stream.bitrate || 2500,
      fps: stream.fps || 30,
      start_time: stream.start_time,
      end_time: stream.end_time || new Date().toISOString(),
      duration: durationSeconds,
      use_advanced_settings: stream.use_advanced_settings ? 1 : 0,
      user_id: stream.user_id
    };

    return new Promise((resolve, reject) => {
      const { db } = require('../db/database');
      db.run(
        `INSERT INTO stream_history (
          id, stream_id, title, platform, platform_icon, video_id, video_title,
          resolution, bitrate, fps, start_time, end_time, duration, use_advanced_settings, user_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          historyData.id, historyData.stream_id, historyData.title,
          historyData.platform, historyData.platform_icon, historyData.video_id, historyData.video_title,
          historyData.resolution, historyData.bitrate, historyData.fps,
          historyData.start_time, historyData.end_time, historyData.duration,
          historyData.use_advanced_settings, historyData.user_id
        ],
        function (err) {
          if (err) {
            console.error('[StreamingService] Error saving stream history:', err.message);
            return reject(err);
          }
          console.log(`[StreamingService] Stream history saved for stream ${stream.id}, duration: ${Math.floor(durationSeconds/60)}m ${durationSeconds%60}s`);
          resolve(historyData);
        }
      );
    });
  } catch (error) {
    console.error('[StreamingService] Failed to save stream history:', error);
    return false;
  }
}
module.exports = {
  startStream,
  stopStream,
  forceStopStream,
  isStreamActive,
  validateStreamProcess,
  getActiveStreams,
  getStreamLogs,
  syncStreamStatuses,
  saveStreamHistory,
  restoreActiveStreams,
  cleanupOrphanedStreams: restoreActiveStreams, // Alias for the cleanup function
  clearFailedStream,
  forceCleanupOrphanedProcesses,
  // Memory management functions
  cleanupStreamData,
  performPeriodicCleanup,
  performStreamHealthCheck,
  // Load balancing functions
  applyLoadBalancing,
  getLoadBalanceStatus,
  setLoadBalancingEnabled: (enabled) => { loadBalancingEnabled = enabled; },
  updateLoadBalanceConfig: (config) => {
    if (config.thresholds) Object.assign(LOAD_BALANCE_CONFIG.CPU_THRESHOLDS, config.thresholds);
    if (config.qualityPresets) Object.assign(LOAD_BALANCE_CONFIG.QUALITY_PRESETS, config.qualityPresets);
    if (config.checkInterval) LOAD_BALANCE_CONFIG.CPU_CHECK_INTERVAL = config.checkInterval;
    if (config.cooldown) LOAD_BALANCE_CONFIG.QUALITY_CHANGE_COOLDOWN = config.cooldown;
  },
  // Copy mode compatibility functions
  getCopyModeCompatibleSettings,
  needsReencoding,

  // File handle health monitoring functions
  setupFileHandleHealthMonitoring,
  trackFileHandleHealthError,

  // Enhanced RTMP options functions
  getEnhancedRTMPOptions,
  getYouTubeInputOptions,
  getYouTubeOutputOptions
};

// Setup file handle health monitoring for copy mode streams
function setupFileHandleHealthMonitoring(streamId) {
  // Initialize health data tracking
  const initialMemory = process.memoryUsage();
  const healthData = {
    startTime: Date.now(),
    initialMemoryUsage: initialMemory.heapUsed,
    initialRSS: initialMemory.rss,
    errorCount: 0,
    lastErrorReset: Date.now(),
    performanceBaseline: null,
    lastPerformanceCheck: Date.now(),
    degradationWarnings: 0,
    lastDegradationAlert: 0
  };

  fileHandleHealthData.set(streamId, healthData);

  // Start monitoring after delay to allow stream stabilization
  const startTimer = setTimeout(() => {
    if (!activeStreams.has(streamId)) {
      return; // Stream no longer active
    }

    addStreamLog(streamId, `[FileHandleHealth] Starting file handle health monitoring for copy mode stream`);

    const performHealthCheck = () => {
      if (!activeStreams.has(streamId)) {
        addStreamLog(streamId, `[FileHandleHealth] Stream no longer active, stopping health monitoring`);
        return; // Stream stopped, cleanup will be handled elsewhere
      }

      const currentHealthData = fileHandleHealthData.get(streamId);
      if (!currentHealthData) {
        addStreamLog(streamId, `[FileHandleHealth] Health data not found, stopping monitoring`);
        return;
      }

      const ffmpegProcess = activeStreams.get(streamId);
      const isYouTube = ffmpegProcess && ffmpegProcess.isYouTube;

      // Perform health assessment
      const healthAssessment = assessFileHandleHealth(streamId, currentHealthData, isYouTube);

      if (healthAssessment.needsAction) {
        handleFileHandleDegradation(streamId, healthAssessment, isYouTube);
      } else if (healthAssessment.hasWarnings) {
        addStreamLog(streamId, `[FileHandleHealth] Warning: ${healthAssessment.warningMessage}`);
      }

      // Reset error count hourly
      const now = Date.now();
      if (now - currentHealthData.lastErrorReset > 60 * 60 * 1000) {
        currentHealthData.errorCount = 0;
        currentHealthData.lastErrorReset = now;
        fileHandleHealthData.set(streamId, currentHealthData);
      }

      // Schedule next health check
      const nextTimer = setTimeout(performHealthCheck, FILE_HANDLE_HEALTH_CONFIG.MONITORING_INTERVAL);
      streamHealthTimers.set(streamId + '_filehandle', nextTimer);
    };

    // Start the health check cycle
    performHealthCheck();

  }, FILE_HANDLE_HEALTH_CONFIG.MONITORING_START_DELAY);

  streamHealthTimers.set(streamId + '_filehandle', startTimer);
}

// Assess file handle health for a stream
function assessFileHandleHealth(streamId, healthData, isYouTube) {
  const currentMemory = process.memoryUsage();
  const now = Date.now();
  const streamAge = now - healthData.startTime;

  // Calculate memory growth
  const heapGrowth = currentMemory.heapUsed - healthData.initialMemoryUsage;
  const rssGrowth = currentMemory.rss - healthData.initialRSS;

  // Determine thresholds based on stream type
  const memoryThreshold = isYouTube ?
    FILE_HANDLE_HEALTH_CONFIG.MEMORY_GROWTH_THRESHOLD * 1.5 :
    FILE_HANDLE_HEALTH_CONFIG.MEMORY_GROWTH_THRESHOLD;

  const errorThreshold = isYouTube ?
    FILE_HANDLE_HEALTH_CONFIG.YOUTUBE_MANUAL_INTERVENTION_THRESHOLD :
    FILE_HANDLE_HEALTH_CONFIG.ERROR_FREQUENCY_THRESHOLD;

  const criticalErrorThreshold = isYouTube ?
    FILE_HANDLE_HEALTH_CONFIG.YOUTUBE_MANUAL_INTERVENTION_THRESHOLD + 10 :
    FILE_HANDLE_HEALTH_CONFIG.CRITICAL_ERROR_THRESHOLD;

  // Assessment results
  const assessment = {
    needsAction: false,
    hasWarnings: false,
    warningMessage: '',
    actionReason: '',
    metrics: {
      heapGrowth: Math.round(heapGrowth / 1024 / 1024), // MB
      rssGrowth: Math.round(rssGrowth / 1024 / 1024), // MB
      errorCount: healthData.errorCount,
      streamAgeHours: Math.round(streamAge / 1000 / 60 / 60 * 10) / 10 // Hours with 1 decimal
    }
  };

  // Check for critical memory growth
  if (heapGrowth > memoryThreshold || currentMemory.heapUsed > FILE_HANDLE_HEALTH_CONFIG.ABSOLUTE_MEMORY_THRESHOLD) {
    assessment.needsAction = true;
    assessment.actionReason = `Memory growth exceeded threshold: Heap +${assessment.metrics.heapGrowth}MB, RSS +${assessment.metrics.rssGrowth}MB`;
  }

  // Check for critical error frequency
  else if (healthData.errorCount > criticalErrorThreshold) {
    assessment.needsAction = true;
    assessment.actionReason = `Critical error frequency: ${healthData.errorCount} errors in the last hour`;
  }

  // Check for warning conditions
  else if (heapGrowth > memoryThreshold * 0.7) {
    assessment.hasWarnings = true;
    assessment.warningMessage = `Memory growth approaching threshold: Heap +${assessment.metrics.heapGrowth}MB (${Math.round(heapGrowth/memoryThreshold*100)}% of threshold)`;
    healthData.degradationWarnings++;
  }

  else if (healthData.errorCount > errorThreshold) {
    assessment.hasWarnings = true;
    assessment.warningMessage = `Error frequency elevated: ${healthData.errorCount} errors in the last hour`;
    healthData.degradationWarnings++;
  }

  // Log periodic health status for long-running streams
  if (streamAge > 4 * 60 * 60 * 1000) { // After 4 hours
    const statusInterval = 4 * 60 * 60 * 1000; // Every 4 hours
    if (now - healthData.lastPerformanceCheck > statusInterval) {
      addStreamLog(streamId, `[FileHandleHealth] Status: Age ${assessment.metrics.streamAgeHours}h, Heap +${assessment.metrics.heapGrowth}MB, Errors ${assessment.metrics.errorCount}/h, Warnings ${healthData.degradationWarnings}`);
      healthData.lastPerformanceCheck = now;
    }
  }

  return assessment;
}

// Handle file handle degradation based on stream type
function handleFileHandleDegradation(streamId, assessment, isYouTube) {
  const now = Date.now();

  addStreamLog(streamId, `[FileHandleDegradation] Detected: ${assessment.actionReason}`);
  addStreamLog(streamId, `[FileHandleDegradation] Metrics: Heap +${assessment.metrics.heapGrowth}MB, RSS +${assessment.metrics.rssGrowth}MB, Errors ${assessment.metrics.errorCount}/h, Age ${assessment.metrics.streamAgeHours}h`);

  if (isYouTube) {
    // For YouTube streams, log the issue but don't auto-restart to avoid viewer disconnection
    const lastAlert = youtubeAlertCooldowns.get(streamId) || 0;
    const cooldownPeriod = FILE_HANDLE_HEALTH_CONFIG.YOUTUBE_ALERT_COOLDOWN;

    if (now - lastAlert > cooldownPeriod) {
      addStreamLog(streamId, `[FileHandleDegradation] YouTube stream detected - MANUAL INTERVENTION RECOMMENDED`);
      addStreamLog(streamId, `[FileHandleDegradation] Recommendation: Consider manually restarting this stream during low-traffic period`);
      addStreamLog(streamId, `[FileHandleDegradation] Stream will continue running to avoid viewer disconnection`);

      console.log(`⚠️ [FileHandleHealth] YouTube stream ${streamId} needs attention: ${assessment.actionReason}`);
      console.log(`📊 [FileHandleHealth] Metrics - Heap: +${assessment.metrics.heapGrowth}MB, Errors: ${assessment.metrics.errorCount}/h, Age: ${assessment.metrics.streamAgeHours}h`);
      console.log(`🔧 [FileHandleHealth] Manual restart recommended during low-traffic period`);

      // Set cooldown to prevent alert spam
      youtubeAlertCooldowns.set(streamId, now);

      // Could integrate with external monitoring/alerting system here
      // Example: sendAlertToMonitoringSystem(streamId, assessment);

    } else {
      const remainingCooldown = Math.round((cooldownPeriod - (now - lastAlert)) / 1000 / 60);
      addStreamLog(streamId, `[FileHandleDegradation] YouTube alert in cooldown (${remainingCooldown} minutes remaining)`);
    }

  } else {
    // For non-YouTube streams, we can safely perform seamless handoff
    addStreamLog(streamId, `[FileHandleDegradation] Non-YouTube stream - attempting seamless process handoff`);

    performSeamlessHandoffForDegradation(streamId, assessment.actionReason);
  }
}

// Perform seamless handoff for non-YouTube streams experiencing degradation
async function performSeamlessHandoffForDegradation(streamId, reason) {
  try {
    addStreamLog(streamId, `[SeamlessHandoff] Starting seamless process handoff: ${reason}`);

    const currentProcess = activeStreams.get(streamId);
    if (!currentProcess) {
      addStreamLog(streamId, `[SeamlessHandoff] No active process found, aborting handoff`);
      return;
    }

    const stream = await Stream.findById(streamId);
    if (!stream) {
      addStreamLog(streamId, `[SeamlessHandoff] Stream not found in database, aborting handoff`);
      return;
    }

    // Build new FFmpeg command (maintaining copy mode)
    const newFFmpegArgs = await buildFFmpegArgs(stream);

    // Verify we're still using copy mode
    if (!newFFmpegArgs.includes('-c:v') || !newFFmpegArgs.includes('copy')) {
      addStreamLog(streamId, `[SeamlessHandoff] Copy mode not detected in new args, aborting handoff to maintain performance`);
      console.log(`⚠️ [SeamlessHandoff] Stream ${streamId} handoff aborted - copy mode not maintained`);
      return;
    }

    addStreamLog(streamId, `[SeamlessHandoff] Starting new process while maintaining current stream`);

    // Start new process with CPU allocation
    const cpuAllocatedArgs = cpuManager.addStreamingCPUAllocation([...newFFmpegArgs]);
    const newProcess = cpuManager.spawnWithCPUAffinity(ffmpegPath, cpuAllocatedArgs, {
      detached: false,
      stdio: ['ignore', 'pipe', 'pipe']
    }, 'streaming');

    // Set up new process properties
    newProcess.startTime = Date.now();
    newProcess.streamStartTime = Date.now();
    newProcess.isYouTube = currentProcess.isYouTube;

    // Wait for new process to establish RTMP connection
    const connectionEstablished = await waitForRTMPConnection(newProcess, 15000); // 15 second timeout

    if (connectionEstablished) {
      addStreamLog(streamId, `[SeamlessHandoff] New process connected successfully, replacing old process`);

      // Replace the process in activeStreams
      activeStreams.set(streamId, newProcess);

      // Gracefully terminate old process
      try {
        currentProcess.kill('SIGTERM');
        addStreamLog(streamId, `[SeamlessHandoff] Old process terminated gracefully`);
      } catch (killError) {
        addStreamLog(streamId, `[SeamlessHandoff] Error terminating old process: ${killError.message}`);
      }

      // Reset health data for new process
      const newHealthData = {
        startTime: Date.now(),
        initialMemoryUsage: process.memoryUsage().heapUsed,
        initialRSS: process.memoryUsage().rss,
        errorCount: 0,
        lastErrorReset: Date.now(),
        performanceBaseline: null,
        lastPerformanceCheck: Date.now(),
        degradationWarnings: 0,
        lastDegradationAlert: 0
      };
      fileHandleHealthData.set(streamId, newHealthData);

      // Set up monitoring for new process
      setupLongDurationHealthCheck(streamId);

      addStreamLog(streamId, `[SeamlessHandoff] Handoff completed successfully - file handles refreshed`);
      console.log(`✅ [SeamlessHandoff] Stream ${streamId} handoff successful: ${reason}`);

    } else {
      // Rollback: Kill new process, keep old one
      addStreamLog(streamId, `[SeamlessHandoff] New process failed to connect, keeping original process`);

      try {
        newProcess.kill('SIGKILL');
      } catch (killError) {
        addStreamLog(streamId, `[SeamlessHandoff] Error killing failed new process: ${killError.message}`);
      }

      console.log(`❌ [SeamlessHandoff] Stream ${streamId} handoff failed - keeping original process`);
    }

  } catch (error) {
    addStreamLog(streamId, `[SeamlessHandoff] Error during handoff: ${error.message}`);
    console.error(`[SeamlessHandoff] Error for stream ${streamId}: ${error.message}`);
  }
}

// Wait for RTMP connection to be established
async function waitForRTMPConnection(process, timeoutMs) {
  return new Promise((resolve) => {
    let resolved = false;
    const timeout = setTimeout(() => {
      if (!resolved) {
        resolved = true;
        resolve(false);
      }
    }, timeoutMs);

    // Listen for connection success indicators in stderr
    const onStderrData = (data) => {
      if (resolved) return;

      const output = data.toString();

      // Look for RTMP connection success indicators
      const connectionIndicators = [
        'Stream mapping:',
        'Press [q] to stop',
        'frame=',
        'Output #0',
        'Stream #0:0',
        'fps='
      ];

      const hasConnectionIndicator = connectionIndicators.some(indicator =>
        output.includes(indicator)
      );

      if (hasConnectionIndicator) {
        resolved = true;
        clearTimeout(timeout);
        process.stderr.removeListener('data', onStderrData);
        resolve(true);
      }
    };

    // Listen for process errors
    const onProcessError = (error) => {
      if (!resolved) {
        resolved = true;
        clearTimeout(timeout);
        process.stderr.removeListener('data', onStderrData);
        process.removeListener('error', onProcessError);
        resolve(false);
      }
    };

    process.stderr.on('data', onStderrData);
    process.on('error', onProcessError);

    // Also resolve false if process exits early
    process.on('exit', (code) => {
      if (!resolved) {
        resolved = true;
        clearTimeout(timeout);
        resolve(false);
      }
    });
  });
}

// Track file handle health errors from FFmpeg stderr
function trackFileHandleHealthError(streamId, errorMessage) {
  const healthData = fileHandleHealthData.get(streamId);
  if (healthData) {
    healthData.errorCount++;
    fileHandleHealthData.set(streamId, healthData);
  }
}

schedulerService.init(module.exports);